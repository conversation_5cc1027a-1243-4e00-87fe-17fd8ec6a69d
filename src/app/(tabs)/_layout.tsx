import { Tabs } from 'expo-router';
import React from 'react';
import 'global.css';
import { Platform } from 'react-native';
import { useTranslation } from 'react-i18next';

import { HapticTab } from '@/components/HapticTab';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { useTheme, Tokens } from '@/theme';
import { JellyIcon } from '@/components/ui/JellyIcon';
import { navConfig } from '@/config/navigation';

export default function TabLayout() {
	const theme = useTheme();
	const { t } = useTranslation();

	return (
		<Tabs
			screenOptions={{
				tabBarActiveTintColor: theme.colors.tint,
				headerShown: true,
				headerStyle: {
					backgroundColor: theme.colors.background,
				},
				headerTintColor: theme.colors.text,
				headerTitleStyle: {
					...theme.fonts.bold,
				},
				tabBarButton: HapticTab,
				tabBarBackground: TabBarBackground,
				tabBarStyle: Platform.select({
					ios: {
						// Use a transparent background on iOS to show the blur effect
						position: 'absolute',
					},
					default: {},
				}),
			}}
		>
			{navConfig.map((navItem) => (
				<Tabs.Screen
					key={navItem.name}
					name={navItem.name}
					options={{
						title: t(navItem.translationKey),
						headerShown:
							navItem.name === 'more' || navItem.name === 'home' ? false : true,
						tabBarIcon: ({ color }) => (
							<JellyIcon
								name={navItem.icon as any}
								variant={navItem.iconVariant as any}
								color={color}
								size={Tokens.sizes.icon}
							/>
						),
						href: navItem.pageUrl || null, // Ensure proper routing
					}}
				/>
			))}
		</Tabs>
	);
}
