import React from 'react';
import { useTranslation } from 'react-i18next';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';
import MoreIndex from '@/components/more/MoreIndex';

// This screen shows the More tab listing
export default function MoreScreen() {
	const { t } = useTranslation();

	// Set up header styling for More tab (no back button, left-aligned)
	useHeaderNavStyle({
		title: t('nav_main.more'),
		showBackButton: false,
		headerTitleAlign: 'left',
	});

	return <MoreIndex />;
}
