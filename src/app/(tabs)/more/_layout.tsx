import { Stack } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';

export default function MoreStackLayout() {
	const { t } = useTranslation();

	return (
		<Stack
			screenOptions={{
				headerShown: false, // Let individual screens handle their headers
			}}
		>
			<Stack.Screen
				name='index'
				options={{
					title: t('nav_main.more'),
				}}
			/>
			<Stack.Screen
				name='[...slug]'
				options={{
					title: 'More',
				}}
			/>
		</Stack>
	);
}
