import React from 'react';
import { useLocalSearchParams } from 'expo-router';
import { StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';
import { getFeatureComponent, getTranslationKey } from '@/config/navigation';

export default function DynamicMoreScreen() {
	const { slug } = useLocalSearchParams<{ slug: string[] }>();
	const { t } = useTranslation();

	// Get the route path (e.g., 'events', 'settings/notification-preferences')
	const routePath = Array.isArray(slug) ? slug.join('/') : slug || '';

	// Get the title for the header using translations
	const translationKey = getTranslationKey(routePath);
	const title = translationKey ? t(translationKey) : 'More';

	// Set up consistent header styling
	useHeaderNavStyle({ title });

	// Get the component for this route
	const FeatureComponent = getFeatureComponent(routePath);

	if (FeatureComponent) {
		return <FeatureComponent />;
	}

	// Fallback for routes that don't have feature components yet
	return (
		<ThemedView style={styles.container}>
			<ThemedText type='title'>{title}</ThemedText>
			<ThemedText style={styles.description}>
				{t('features.comingSoon', { routePath })}
			</ThemedText>
			<ThemedText type='default' style={styles.routeInfo}>
				Route: /{routePath}
			</ThemedText>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
	description: {
		textAlign: 'center',
		marginTop: 16,
		opacity: 0.7,
	},
	routeInfo: {
		textAlign: 'center',
		marginTop: 8,
		opacity: 0.5,
		fontFamily: 'monospace',
	},
});
