import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { GroupEventsScreen } from '@/features/events';
import { JellyIcon } from '@/components/ui/JellyIcon';

/**
 * Group Events Route
 *
 * This is a thin routing wrapper that configures navigation for the Group Events screen.
 * All UI logic lives in the GroupEventsScreen component.
 */
export default function GroupEventsRoute() {
	const { t } = useTranslation();

	return (
		<>
			<Stack.Screen
				options={{
					title: t('features.events.groupEvents'),
					headerShown: true,
					headerRight: () => (
						<TouchableOpacity
							style={styles.iconButton}
							onPress={() => console.log('Search button pressed')}
							accessibilityLabel={t('features.events.a11y.searchEvents')}
						>
							<JellyIcon name='search' variant='regular' color='#777777' size={22} />
						</TouchableOpacity>
					),
				}}
			/>
			<GroupEventsScreen />
		</>
	);
}

const styles = StyleSheet.create({
	iconButton: {
		padding: 8,
		marginRight: 8,
		borderRadius: 8,
		justifyContent: 'center',
		alignItems: 'center',
	},
});
