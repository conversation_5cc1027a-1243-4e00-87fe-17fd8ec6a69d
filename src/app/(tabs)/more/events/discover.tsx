import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { DiscoverEventsScreen } from '@/features/events';
import { JellyIcon } from '@/components/ui/JellyIcon';

/**
 * Discover Events Route
 *
 * This is a thin routing wrapper that configures navigation for the Discover Events screen.
 * All UI logic lives in the DiscoverEventsScreen component.
 */
export default function DiscoverEventsRoute() {
	const { t } = useTranslation();

	return (
		<>
			<Stack.Screen
				options={{
					title: t('features.events.discoverEvents'),
					headerShown: true,
					headerRight: () => (
						<TouchableOpacity
							style={styles.iconButton}
							onPress={() => console.log('Search button pressed')}
							accessibilityLabel={t('features.events.a11y.searchEvents')}
						>
							<JellyIcon name='search' variant='regular' color='#777777' size={22} />
						</TouchableOpacity>
					),
				}}
			/>
			<DiscoverEventsScreen />
		</>
	);
}

const styles = StyleSheet.create({
	iconButton: {
		padding: 8,
		marginRight: 8,
		borderRadius: 8,
		justifyContent: 'center',
		alignItems: 'center',
	},
});
