import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Stack, useLocalSearchParams } from 'expo-router';
import { useTranslation } from 'react-i18next';

// Import the existing EventDetailScreen component
import { EventDetailScreen } from '@/features/events';

/**
 * Event Detail Screen Wrapper for Tab Navigation
 *
 * This component wraps the EventDetailScreen component from the events feature
 * and handles parameter passing from the tab navigation.
 */
export default function EventDetailRoute() {
	const { t } = useTranslation();
	const { id } = useLocalSearchParams<{ id: string }>();

	return (
		<>
			<Stack.Screen
				options={{
					title: t('features.events.eventDetails'),
					headerShown: true,
					// Using the built-in back button from Stack navigation
					headerTintColor: '#777777', // Grey color for wireframe look
					headerStyle: {
						backgroundColor: '#f5f5f5', // Light grey background for wireframe look
					},
					headerTitleStyle: {
						color: '#333333', // Darker text for contrast
						fontSize: 17,
						fontWeight: '600',
					},
				}}
			/>
			<View style={styles.container}>
				<EventDetailScreen eventId={id as string} />
			</View>
		</>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
});
