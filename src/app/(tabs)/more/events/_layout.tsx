import { Stack } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { EventsHeader } from '@/features/events/components';

/**
 * Layout for the events routes
 *
 * This layout ensures proper navigation for all event screens
 * and provides headers with standard back navigation.
 */
export default function EventsLayout() {
	const { t } = useTranslation();

	return (
		<Stack
			screenOptions={{
				headerShown: true,
				// Use standard back button handling with consistent styling
				headerBackVisible: true,
				headerTintColor: '#777777', // Grey color for wireframe look
				headerStyle: {
					backgroundColor: '#f5f5f5', // Light grey background for wireframe look
				},
				headerTitleStyle: {
					color: '#333333', // Darker text for contrast
					fontSize: 17,
					fontWeight: '600',
				},
			}}
		>
			<Stack.Screen
				name='index'
				options={{
					title: t('features.events.title'),
					// Main events screen doesn't need a back button
					headerLeft: undefined,
					headerRight: () => <EventsHeader />,
				}}
			/>
			<Stack.Screen
				name='[id]'
				options={{
					title: t('features.events.eventDetails'),
				}}
			/>
			<Stack.Screen
				name='search'
				options={{
					title: t('features.events.searchEvents'),
				}}
			/>
			<Stack.Screen
				name='your-events'
				options={{
					title: t('features.events.yourEvents'),
				}}
			/>
			<Stack.Screen
				name='group-events'
				options={{
					title: t('features.events.groupEvents'),
				}}
			/>
			<Stack.Screen
				name='discover'
				options={{
					title: t('features.events.discoverEvents'),
				}}
			/>
			<Stack.Screen
				name='past'
				options={{
					title: t('features.events.pastEvents'),
				}}
			/>
		</Stack>
	);
}
