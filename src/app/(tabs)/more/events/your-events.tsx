import React from 'react';
import { TouchableOpacity, StyleSheet } from 'react-native';
import { Stack } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { YourEventsScreen } from '@/features/events';
import { JellyIcon } from '@/components/ui/JellyIcon';

/**
 * Your Events Route
 *
 * This is a thin routing wrapper that configures navigation for the Your Events screen.
 * All UI logic lives in the YourEventsScreen component.
 */
export default function YourEventsRoute() {
	const { t } = useTranslation();

	return (
		<>
			<Stack.Screen
				options={{
					title: t('features.events.yourEvents'),
					headerShown: true,
					headerRight: () => (
						<TouchableOpacity
							style={styles.iconButton}
							onPress={() => console.log('Search button pressed')}
							accessibilityLabel={t('features.events.a11y.searchEvents')}
						>
							<JellyIcon name='search' variant='regular' color='#777777' size={22} />
						</TouchableOpacity>
					),
				}}
			/>
			<YourEventsScreen />
		</>
	);
}

const styles = StyleSheet.create({
	iconButton: {
		padding: 8,
		marginRight: 8,
		borderRadius: 8,
		justifyContent: 'center',
		alignItems: 'center',
	},
});
