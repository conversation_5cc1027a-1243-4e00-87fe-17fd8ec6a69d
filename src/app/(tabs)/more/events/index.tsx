import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Events } from '@/features/events';

/**
 * Main Events Screen for the More tab
 *
 * This component renders the Events feature component.
 * The Events component internally uses EventsScreen which contains
 * all the business logic and UI for the events feature.
 */
export default function EventsRoute() {
	return (
		<View style={styles.container}>
			<Events />
		</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
});
