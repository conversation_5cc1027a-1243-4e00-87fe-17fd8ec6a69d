import { Stack } from 'expo-router';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { navHeaderConfig } from '@/config/navigation';

export default function HomeStackLayout() {
	const { t } = useTranslation();

	return (
		<Stack
			screenOptions={{
				headerShown: false, // Let individual screens handle their headers
			}}
		>
			{navHeaderConfig.map((navItem) => (
				<Stack.Screen
					key={navItem.name}
					name={navItem.name}
					options={{
						title: t(navItem.translationKey),
						presentation: navItem.presentation,
					}}
				/>
			))}
		</Stack>
	);
}
