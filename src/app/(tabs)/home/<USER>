import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';

export default function SearchScreen() {
	useHeaderNavStyle({ title: 'Search' });

	return (
		<ThemedView style={styles.container}>
			<ThemedText type='title'>Search</ThemedText>
			<ThemedText>Search functionality coming soon...</ThemedText>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
});
