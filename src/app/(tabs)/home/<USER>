import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';

export default function NotificationsScreen() {
	useHeaderNavStyle({ title: 'Notifications' });

	return (
		<ThemedView style={styles.container}>
			<ThemedText type='title'>Notifications</ThemedText>
			<ThemedText>Your notifications will appear here...</ThemedText>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
});
