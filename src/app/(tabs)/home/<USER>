import React from 'react';
import { StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import PostComposer from '@/app/social/post-composer';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';

export default function CreatePostScreen() {
	useHeaderNavStyle({ title: 'Create Post' });

	return (
		<ThemedView style={styles.container}>
			<PostComposer />
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 16,
	},
});
