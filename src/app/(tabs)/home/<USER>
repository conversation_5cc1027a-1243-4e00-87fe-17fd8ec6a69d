import { Image } from 'expo-image';
import { StyleSheet } from 'react-native';
import { useSelector } from 'react-redux';

import ParallaxScrollView from '@/components/ParallaxScrollView';
import HeaderNav from '@/components/navigation/HeaderNav';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { selectUser } from '@/features/user/userSlice';
import { Tokens } from '@/theme';
import { PostsList } from '@/features/posts';
export default function HomeScreen() {
	const user = useSelector(selectUser);

	return (
		<>
			<HeaderNav />
			<ParallaxScrollView
				headerBackgroundColor={{ light: '#A1CEDC', dark: '#1D3D47' }}
				headerImage={
					<Image source={require('@assets/images/logo.svg')} style={styles.reactLogo} />
				}
				// overlay={<PostComposerButton />}
			>
				<ThemedView style={styles.greetingContainer}>
					<ThemedText type='defaultSemiBold'>Hello {user?.email ?? 'Guest'}</ThemedText>
					{user && (
						<ThemedText type='default' style={styles.userInfo}>
							Account ID: {user.una_account_id} | Role: {user.role} | Status:
							{user.status}
						</ThemedText>
					)}
				</ThemedView>
				<PostsList />
			</ParallaxScrollView>
		</>
	);
}

const styles = StyleSheet.create({
	greetingContainer: {
		marginBottom: Tokens.spacing.sm,
		padding: Tokens.spacing.md,
		borderRadius: Tokens.radius.sm,
		backgroundColor: 'rgba(161, 206, 220, 0.1)',
	},
	userInfo: {
		marginBottom: Tokens.spacing.sm,
		opacity: 0.8,
	},
	titleContainer: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: Tokens.spacing.sm,
	},
	stepContainer: {
		gap: Tokens.spacing.sm,
		marginBottom: Tokens.spacing.sm,
	},
	reactLogo: {
		height: 48,
		width: 96,
		top: Tokens.spacing.lg,
		left: Tokens.spacing.md,
		position: 'absolute',
	},
});
