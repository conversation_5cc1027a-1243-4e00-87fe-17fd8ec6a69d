{"nav_main": {"home": "Home", "profile": "Profile", "calendar": "Calendar", "groups": "Groups", "more": "More"}, "nav_sub": {"events": "Events", "saved": "Saved", "friendsNearMe": "Friends Near Me", "messenger": "<PERSON>", "resourceLibrary": "Resource Library", "settings": "Settings"}, "nav_header": {"createPost": "Create Post", "search": "Search", "messenger": "<PERSON>", "notifications": "Notifications"}, "nav_settings": {"notificationPreferences": "Notification Preferences", "privacySettings": "Privacy Settings", "updatePassword": "Update Password", "managePosts": "Manage Posts", "manageResources": "Manage Resources", "colorMode": "Color Mode"}, "tabs": {"home": "Home", "explore": "Explore", "apiKeys": "API Keys"}, "dev_home": {"welcome": "Welcome", "waveAgain": "Wave Again", "config": "Config", "navigation": "Navigation", "navigateButton": "Click to navigate", "apiKeysHeader": "A<PERSON> <PERSON>", "apiKeysButton": "<PERSON><PERSON>e <PERSON>"}, "dev_hello": {"title": "Hello Screen", "defaultMessage": "Hello World", "buttonAlert": "Click Me", "sendMessage": "Send Message", "reset": "Reset", "happyDay": "🎈Happy Day🎈"}, "dev_explore": {"title": "Explore", "intro": "This app includes example code to help you get started.", "fileRouting": {"title": "File-based routing", "desc1": "This app has two screens: app/(tabs)/index.tsx and app/(tabs)/explore.tsx", "desc2": "The layout file in app/(tabs)/_layout.tsx sets up the tab navigator.", "learnMore": "Learn more"}, "platformSupport": {"title": "Android, iOS, and web support", "desc": "You can open this project on Android, iOS, and the web. To open the web version, press \"w\" in the terminal running this project."}, "images": {"title": "Images", "desc": "For static images, you can use the @2x and @3x suffixes to provide files for different screen densities", "learnMore": "Learn more"}, "fonts": {"title": "Custom fonts", "desc1": "Open app/_layout.tsx to see how to load custom fonts such as this one.", "learnMore": "Learn more"}, "themes": {"title": "Light and dark mode components", "desc": "This template has light and dark mode support. The useColorScheme() hook lets you inspect what the user's current color scheme is, and so you can adjust UI colors accordingly.", "learnMore": "Learn more"}, "animations": {"title": "Animations", "desc1": "This template includes an example of an animated component. The components/HelloWave.tsx component uses the powerful react-native-reanimated library to create a waving hand animation.", "desc2": "The components/ParallaxScrollView.tsx component provides a parallax effect for the header image."}}, "social": {"postComposer": {"placeholder": "What's on your mind?", "submit": "Post", "submitting": "Posting...", "error": "Failed to create post. Please try again.", "success": "Post created successfully!", "title": "Create Post"}}, "common": {"learnMore": "Learn more", "ok": "OK", "cancel": "Cancel", "error": "Error"}, "auth": {"loadingAuth": "Loading authentication...", "signOut": "Sign Out", "signOutConfirmation": "Are you sure you want to sign out?"}, "features": {"calendar": {"title": "Calendar", "description": "View and manage your events, appointments, and schedule in an organized calendar interface."}, "groups": {"title": "Groups", "description": "Connect with communities, join interest-based groups, and participate in group discussions."}, "profile": {"title": "Profile", "description": "Manage your personal information, preferences, and account settings."}, "events": {"title": "Events", "description": "This component is dynamically loaded from features/events/ directory and can be reused across different parts of the app.", "eventDetails": "Event Details", "searchEvents": "Search Events", "yourEvents": "Your Events", "groupEvents": "From your Groups & Communities", "discoverEvents": "Discover Events", "pastEvents": "Past Events", "myGroupsAndCommunities": "My Groups & Communities", "viewAll": "View All", "yourEventsLabel": "Your Events", "groupEventsLabel": "Group Events", "discoverEventsLabel": "Discover Events", "pastEventsLabel": "Past Events", "a11y": {"viewEventDetails": "Double tap to view event details", "messageOrganizer": "Message event organizer", "searchEvents": "Search events", "filterEvents": "Filter events"}, "location": {"virtual": "Virtual", "virtualEvent": "Virtual Event"}, "labels": {"location": "Location", "rsvp": "RSVP", "date": "Date", "time": "Time", "attendees": "Attendees", "description": "Description"}, "attendees": {"none": "No attendees yet", "one": "1 attendee", "many": "{{count}} attendees"}, "rsvp": {"going": "Going", "interested": "Interested", "notGoing": "Not Going", "attended": "Attended", "buttonLabel": "RSVP", "modalTitle": "Your Response", "a11y": {"eventPassed": "This event has already passed", "changeStatus": "Double tap to change your RSVP status"}}, "empty": {"yourEvents": {"title": "Nothing you're attending yet", "description": "Tap RSVP to add events to this list.", "description2": "Not ready to commit? Save them for later.", "discoverButton": "Discover Events", "a11y": {"discoverLabel": "Discover events"}}, "noEvents": "No events available"}, "loading": {"events": "Loading events..."}, "errors": {"retry": "Retry", "tryAgain": "Try Again", "resourceNotFound": "Resource not found", "authRequired": "Authentication required", "invalidRequest": "Invalid request data", "serverError": "Server error. Please try again later.", "noConnection": "No internet connection", "fetchEventDetails": "Failed to fetch event details", "updateEventStatus": "Failed to update event status", "loadingEventDetails": "Loading event details..."}}, "saved": {"title": "Saved", "description": "This component manages saved posts, articles, and bookmarked content."}, "friendsNearMe": {"title": "Friends Near Me", "description": "This component shows friends and connections in your local area using location services."}, "messenger": {"title": "<PERSON>", "description": "This component handles direct messaging, chat conversations, and communication features."}, "resourceLibrary": {"title": "Resource Library", "description": "This component provides access to educational resources, documents, and learning materials."}, "settings": {"title": "Settings", "description": "This component manages app preferences, account settings, and configuration options."}, "comingSoon": "Feature component for \"{routePath}\" coming soon..."}, "settings": {"notificationPreferences": {"description": "Manage your notification settings and preferences for alerts, messages, and updates."}, "privacySettings": {"description": "Control your privacy settings, data sharing preferences, and account visibility options."}, "updatePassword": {"description": "Change your account password and manage security settings for your profile."}, "managePosts": {"description": "Manage your posts, edit content, and control the visibility of your shared content."}, "manageResources": {"description": "Organize and manage your saved resources, documents, and learning materials."}, "colorMode": {"description": "Switch between light and dark themes, and customize the app's appearance."}}, "footer": {"communityRules": "Community Rules", "privacy": "Privacy", "termsOfService": "Terms of Service", "copyright": "© Copyright {{year}}", "cannotOpenUrl": "Cannot open this link. Please try again later."}}