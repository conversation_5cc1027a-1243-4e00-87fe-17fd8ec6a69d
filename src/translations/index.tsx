import { createInstance } from 'i18next';
import { initReactI18next } from 'react-i18next';
import { useEffect, useState } from 'react';

import en from './en.json';

const i18n = createInstance();

const resources = {
	en: { translation: en },
};

// Track initialization promise to prevent multiple initializations
let initPromise: Promise<void> | null = null;

/**
 * Initialize i18n asynchronously
 * This function ensures i18n is properly initialized before the app renders
 * Multiple calls will return the same promise to prevent race conditions
 */
export async function initI18n() {
	// Return existing initialization if already complete
	if (i18n.isInitialized) {
		return i18n;
	}

	// Return existing promise if initialization is in progress
	if (initPromise) {
		await initPromise;
		return i18n;
	}

	// Start new initialization
	initPromise = (async () => {
		await i18n.use(initReactI18next).init({
			resources,
			lng: 'en',
			fallbackLng: 'en',
			compatibilityJSON: 'v3' as any, // v3 required for React Native compatibility
			interpolation: { escapeValue: false },
			returnNull: false,
		});
	})();

	try {
		await initPromise;
		return i18n;
	} catch (error) {
		// Reset promise on error so initialization can be retried
		initPromise = null;
		throw error;
	}
}

export function useI18n(): [boolean] {
	const [ready, setReady] = useState<boolean>(i18n.isInitialized);

	useEffect(() => {
		let mounted = true;

		if (!i18n.isInitialized) {
			initI18n().then(() => {
				if (mounted) setReady(true);
			});
		}

		return () => {
			mounted = false;
		};
	}, []);

	return [ready];
}

export async function makeTestI18n(resources: any) {
	const i18n = createInstance();
	await i18n.use(initReactI18next).init({
		resources,
		lng: 'en',
		fallbackLng: 'en',
		interpolation: { escapeValue: false },
		returnNull: false,
	});
	return i18n;
}

export default i18n;
