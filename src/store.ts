import { configureStore } from '@reduxjs/toolkit';
import helloReducer from './features/hello/helloSlice';
import userReducer from './features/user/userSlice';
import postsReducer from './features/posts/postsSlice';
import composePostReducer from './features/posts/composerSlice';
import eventsReducer from './features/events/eventsSlice';
export const store = configureStore({
	reducer: {
		hello: helloReducer,
		user: userReducer,
		posts: postsReducer,
		composePost: composePostReducer,
		events: eventsReducer,
	},
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
