/**
 * Authentication Constants
 *
 * Central location for all authentication-related constants used across the app.
 * Having these values defined in a single file makes it easier to maintain
 * and update authentication parameters.
 */

/**
 * Authentication storage keys
 *
 * All authentication-related storage keys use the 'una_' prefix to:
 * 1. Indicate they are related to the UNA authentication system
 * 2. Avoid conflicts with other storage systems
 * 3. Maintain consistency across the app
 */
export const AUTH_STORAGE_KEYS = {
	AUTH_DATA: 'una_auth_data',
	USER_PROFILE: 'una_user_profile',
	STATE: 'una_auth_state',
	CODE_VERIFIER: 'una_auth_code_verifier',
};

/**
 * Authentication request parameters
 */
export const AUTH_PARAMS = {
	CLIENT_ID: 'mobile_app',
	RESPONSE_TYPE: 'code',
	SCOPE: 'openid profile email',
	CODE_CHALLENGE_METHOD: 'S256',
};

/**
 * Authentication message types for WebView communication
 */
export const AUTH_MESSAGE_TYPES = {
	DEEP_LINK_FOUND: 'DEEP_LINK_FOUND',
	ERROR: 'ERROR',
	TOKEN_RECEIVED: 'TOKEN_RECEIVED',
	AUTH_RESULT: 'AUTH_RESULT',
};

/**
 * Authentication timeouts (in milliseconds)
 */
export const AUTH_TIMEOUTS = {
	// Default token expiry time (1 hour)
	TOKEN_EXPIRY: 60 * 60 * 1000,

	// Request timeout
	REQUEST_TIMEOUT: 30 * 1000,

	// Session timeout
	SESSION_TIMEOUT: 20 * 60 * 1000,

	// Auth flow timeout
	AUTH_FLOW_TIMEOUT: 5 * 60 * 1000,
};

/**
 * Authentication error codes
 */
export const AUTH_ERROR_CODES = {
	TOKEN_EXPIRED: 'token_expired',
	INVALID_TOKEN: 'invalid_token',
	UNAUTHORIZED: 'unauthorized',
	NETWORK_ERROR: 'network_error',
	SERVER_ERROR: 'server_error',
	FORBIDDEN: 'forbidden',
};

/**
 * Authentication HTTP status codes
 */
export const AUTH_HTTP_STATUS = {
	UNAUTHORIZED: 401,
	FORBIDDEN: 403,
	SERVER_ERROR: 500,
};

export default {
	AUTH_STORAGE_KEYS,
	AUTH_PARAMS,
	AUTH_MESSAGE_TYPES,
	AUTH_TIMEOUTS,
	AUTH_ERROR_CODES,
	AUTH_HTTP_STATUS,
};
