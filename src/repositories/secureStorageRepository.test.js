/**
 * Tests for SecureStorageRepository
 */

import { SecureStorageRepository } from './secureStorageRepository';
import { AUTH_STORAGE_KEYS } from '../constants/auth';
import * as SecureStore from 'expo-secure-store';

// Mock the dependencies
jest.mock('expo-secure-store', () => ({
	setItemAsync: jest.fn(),
	getItemAsync: jest.fn(),
	deleteItemAsync: jest.fn(),
}));

jest.mock('react-native', () => ({
	Platform: {
		OS: 'ios', // Default to iOS for SecureStore availability
	},
}));

const mockSecureStore = SecureStore;

describe('SecureStorageRepository', () => {
	let repository;

	beforeEach(() => {
		repository = new SecureStorageRepository();
		jest.clearAllMocks();
		jest.spyOn(console, 'error').mockImplementation(() => {});
		jest.spyOn(console, 'warn').mockImplementation(() => {});
	});

	afterEach(() => {
		jest.restoreAllMocks();
	});

	describe('Basic Storage Operations', () => {
		describe('setItem', () => {
			it('should store a value successfully', async () => {
				mockSecureStore.setItemAsync.mockResolvedValue();

				await repository.setItem('test-key', 'test-value');

				expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith('test-key', 'test-value');
			});

			it('should throw error when storage fails', async () => {
				const error = new Error('Storage failed');
				mockSecureStore.setItemAsync.mockRejectedValue(error);

				await expect(repository.setItem('test-key', 'test-value')).rejects.toThrow(
					'Storage operation failed: Error: Storage failed',
				);
			});
		});

		describe('getItem', () => {
			it('should retrieve a value successfully', async () => {
				mockSecureStore.getItemAsync.mockResolvedValue('test-value');

				const result = await repository.getItem('test-key');

				expect(result).toBe('test-value');
				expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith('test-key');
			});

			it('should return null when item does not exist', async () => {
				mockSecureStore.getItemAsync.mockResolvedValue(null);

				const result = await repository.getItem('test-key');

				expect(result).toBeNull();
			});
		});

		describe('removeItem', () => {
			it('should remove a value successfully', async () => {
				mockSecureStore.deleteItemAsync.mockResolvedValue();

				await repository.removeItem('test-key');

				expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('test-key');
			});
		});
	});

	describe('Object Storage Operations', () => {
		const testObject = { name: 'test', value: 123 };

		describe('setObject', () => {
			it('should store an object successfully', async () => {
				mockSecureStore.setItemAsync.mockResolvedValue();

				await repository.setObject('test-key', testObject);

				expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
					'test-key',
					JSON.stringify(testObject),
				);
			});
		});

		describe('getObject', () => {
			it('should retrieve an object successfully', async () => {
				mockSecureStore.getItemAsync.mockResolvedValue(JSON.stringify(testObject));

				const result = await repository.getObject('test-key');

				expect(result).toEqual(testObject);
			});

			it('should return null when object does not exist', async () => {
				mockSecureStore.getItemAsync.mockResolvedValue(null);

				const result = await repository.getObject('test-key');

				expect(result).toBeNull();
			});
		});

		describe('removeObject', () => {
			it('should remove an object successfully', async () => {
				mockSecureStore.deleteItemAsync.mockResolvedValue();

				await repository.removeObject('test-key');

				expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith('test-key');
			});

			it('should throw error when removal fails', async () => {
				const error = new Error('Removal failed');
				mockSecureStore.deleteItemAsync.mockRejectedValue(error);

				await expect(repository.removeObject('test-key')).rejects.toThrow(
					'Object removal operation failed: Error: Storage operation failed: Error: Removal failed',
				);
			});
		});
	});

	describe('Authentication Data', () => {
		const mockAuthData = {
			token: 'mock-token',
		};

		describe('saveAuthData', () => {
			it('should save authentication data successfully', async () => {
				mockSecureStore.setItemAsync.mockResolvedValue();

				await repository.saveAuthData(mockAuthData);

				expect(mockSecureStore.setItemAsync).toHaveBeenCalledWith(
					AUTH_STORAGE_KEYS.AUTH_DATA,
					JSON.stringify(mockAuthData),
				);
			});

			it('should throw error when storage fails', async () => {
				const error = new Error('Storage failed');
				mockSecureStore.setItemAsync.mockRejectedValue(error);

				await expect(repository.saveAuthData(mockAuthData)).rejects.toThrow(
					'Failed to save authentication data: Error: Object storage operation failed: Error: Storage operation failed: Error: Storage failed',
				);
			});
		});

		describe('getAuthData', () => {
			it('should retrieve authentication data successfully', async () => {
				mockSecureStore.getItemAsync.mockResolvedValue(JSON.stringify(mockAuthData));

				const result = await repository.getAuthData();

				expect(result).toEqual(mockAuthData);
				expect(mockSecureStore.getItemAsync).toHaveBeenCalledWith(
					AUTH_STORAGE_KEYS.AUTH_DATA,
				);
			});

			it('should return null when no data exists', async () => {
				mockSecureStore.getItemAsync.mockResolvedValue(null);

				const result = await repository.getAuthData();

				expect(result).toBeNull();
			});

			it('should return null when storage throws error', async () => {
				mockSecureStore.getItemAsync.mockRejectedValue(new Error('Storage error'));

				const result = await repository.getAuthData();

				expect(result).toBeNull();
			});
		});

		describe('clearAuthData', () => {
			it('should clear all authentication data', async () => {
				mockSecureStore.deleteItemAsync.mockResolvedValue();

				await repository.clearAuthData();

				expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(
					AUTH_STORAGE_KEYS.AUTH_DATA,
				);
			});

			it('should throw error when removal fails', async () => {
				mockSecureStore.deleteItemAsync.mockRejectedValue(new Error('Removal failed'));

				await expect(repository.clearAuthData()).rejects.toThrow(
					'Failed to clear authentication data: Error: Storage operation failed: Error: Removal failed',
				);
			});
		});
	});

	describe('clearAllUserData', () => {
		it('should clear all user-related data', async () => {
			mockSecureStore.deleteItemAsync.mockResolvedValue();

			await repository.clearAllUserData();

			// Verify all auth-related keys are properly cleared
			expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(
				AUTH_STORAGE_KEYS.AUTH_DATA,
			);
			expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(
				AUTH_STORAGE_KEYS.USER_PROFILE,
			);
			expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(AUTH_STORAGE_KEYS.STATE);
			expect(mockSecureStore.deleteItemAsync).toHaveBeenCalledWith(
				AUTH_STORAGE_KEYS.CODE_VERIFIER,
			);
		});

		it('should throw error when clearing fails', async () => {
			mockSecureStore.deleteItemAsync.mockRejectedValue(new Error('Clear failed'));

			await expect(repository.clearAllUserData()).rejects.toThrow(
				'Failed to clear all user data: Error: Storage operation failed: Error: Clear failed',
			);
		});
	});
});
