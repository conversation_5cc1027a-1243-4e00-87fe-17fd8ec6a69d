/**
 * Tab navigation configuration
 *
 * Note: Translation keys reference src/translations/en.json
 * When adding new navigation items, ensure corresponding translations exist in:
 * - en.json (and other language files)
 */

import React from 'react';
import { Events } from '@/features/events';
import { Saved } from '@/features/saved';
import { FriendsNearMe } from '@/features/friends';
import { Messenger } from '@/features/messenger';
import { ResourceLibrary } from '@/features/resources';
import { Settings } from '@/features/settings';
import { Calendar } from '@/features/calendar';
import { Groups } from '@/features/groups';
import { Profile } from '@/features/profile';
import {
	NotificationPreferences,
	PrivacySettings,
	UpdatePassword,
	ManagePosts,
	ManageResources,
	ColorMode,
} from '@/features/settings/components';
import type { NavigationItem } from './navigation.types';
import { resolveUrl } from './navigation.types';

// Re-export types for convenience
export type { NavigationItem };
export { resolveUrl };

// Build navConfig with feature components attached
export const navConfig: NavigationItem[] = [
	{
		name: 'home',
		icon: 'house',
		iconVariant: 'regular',
		translationKey: 'nav_main.home',
		pageUrl: '/(tabs)/home',
	},
	{
		name: 'profile',
		icon: 'user',
		iconVariant: 'regular',
		translationKey: 'nav_main.profile',
		pageUrl: '/(tabs)/profile',
		featureComponent: Profile,
	},
	{
		name: 'calendar',
		icon: 'calendar',
		iconVariant: 'regular',
		translationKey: 'nav_main.calendar',
		pageUrl: '/(tabs)/calendar',
		featureComponent: Calendar,
	},
	{
		name: 'groups',
		icon: 'users',
		iconVariant: 'regular',
		translationKey: 'nav_main.groups',
		pageUrl: '/(tabs)/groups',
		featureComponent: Groups,
	},
	{
		name: 'more',
		icon: 'bars',
		iconVariant: 'regular',
		translationKey: 'nav_main.more',
		pageUrl: '/(tabs)/more',
		subItems: [
			{
				name: 'events',
				icon: 'calendar',
				iconVariant: 'regular',
				translationKey: 'nav_sub.events',
				pageUrl: '/(tabs)/more/events',
				featureComponent: Events,
			},
			{
				name: 'saved',
				icon: 'bookmark',
				iconVariant: 'regular',
				translationKey: 'nav_sub.saved',
				pageUrl: '/saved',
				featureComponent: Saved,
			},
			{
				name: 'friendsNearMe',
				icon: 'map',
				iconVariant: 'regular',
				translationKey: 'nav_sub.friendsNearMe',
				pageUrl: '/friends-near-me',
				featureComponent: FriendsNearMe,
			},
			{
				name: 'messenger',
				icon: 'comments',
				iconVariant: 'regular',
				translationKey: 'nav_sub.messenger',
				pageUrl: '/messenger',
				featureComponent: Messenger,
			},
			{
				name: 'resourceLibrary',
				icon: 'bookOpen',
				iconVariant: 'regular',
				translationKey: 'nav_sub.resourceLibrary',
				pageUrl: '/resource-library',
				featureComponent: ResourceLibrary,
			},
			{
				name: 'settings',
				icon: 'gear',
				iconVariant: 'regular',
				translationKey: 'nav_sub.settings',
				pageUrl: '/settings',
				featureComponent: Settings,
				subItems: [
					{
						name: 'notificationPreferences',
						icon: 'bell',
						iconVariant: 'regular',
						translationKey: 'nav_settings.notificationPreferences',
						pageUrl: '/notificationPreferences',
						featureComponent: NotificationPreferences,
					},
					{
						name: 'privacySettings',
						icon: 'shield',
						iconVariant: 'regular',
						translationKey: 'nav_settings.privacySettings',
						pageUrl: '/privacySettings',
						featureComponent: PrivacySettings,
					},
					{
						name: 'updatePassword',
						icon: 'lock',
						iconVariant: 'regular',
						translationKey: 'nav_settings.updatePassword',
						pageUrl: '/updatePassword',
						featureComponent: UpdatePassword,
					},
					{
						name: 'managePosts',
						icon: 'comments',
						iconVariant: 'regular',
						translationKey: 'nav_settings.managePosts',
						pageUrl: '/managePosts',
						featureComponent: ManagePosts,
					},
					{
						name: 'manageResources',
						icon: 'lightbulb',
						iconVariant: 'regular',
						translationKey: 'nav_settings.manageResources',
						pageUrl: '/manageResources',
						featureComponent: ManageResources,
					},
					{
						name: 'colorMode',
						icon: 'sun',
						iconVariant: 'regular',
						translationKey: 'nav_settings.colorMode',
						pageUrl: '/colorMode',
						featureComponent: ColorMode,
					},
				],
			},
		],
	},
];

// Footer navigation items (for external links and app actions)
export const footerNavConfig = [
	{
		name: 'communityRules',
		translationKey: 'footer.communityRules',
		url: 'https://stride.tiltedwindmillagency.com/about',
		external: true,
	},
	{
		name: 'privacy',
		translationKey: 'footer.privacy',
		url: 'https://stride.tiltedwindmillagency.com/privacy',
		external: true,
	},
	{
		name: 'termsOfService',
		translationKey: 'footer.termsOfService',
		url: 'https://stride.tiltedwindmillagency.com/terms',
		external: true,
	},
	{
		name: 'signOut',
		translationKey: 'auth.signOut',
		action: 'signOut',
	},
	{
		name: 'copyright',
		translationKey: 'footer.copyright',
		action: 'showCopyright',
	},
];

// Helper function to find navigation item by route path (shared logic)
function findNavigationItem(routePath: string): NavigationItem | undefined {
	const moreTab = navConfig.find((item) => item.name === 'more');
	if (!moreTab?.subItems) return undefined;

	// Handle nested routes (e.g., 'events', 'settings/notification-preferences')
	const pathParts = routePath.split('/');
	const mainRoute = pathParts[0];

	// First try to find exact match for full path
	let item = moreTab.subItems.find(
		(item) =>
			item.pageUrl && resolveUrl(moreTab.pageUrl!, item.pageUrl).endsWith(`/${routePath}`),
	);

	// If not found, look for main route and then check its subItems
	if (!item) {
		const mainItem = moreTab.subItems.find(
			(item) =>
				item.name === mainRoute ||
				(item.pageUrl &&
					resolveUrl(moreTab.pageUrl!, item.pageUrl).endsWith(`/${mainRoute}`)),
		);

		if (mainItem && pathParts.length > 1 && mainItem.subItems) {
			// Look in subItems for nested route
			const nestedRoute = pathParts.slice(1).join('/');
			const nestedRouteMain = pathParts[1];

			// Try to find by name first (for settings subitems without pageUrl)
			item = mainItem.subItems.find((subItem) => subItem.name === nestedRouteMain);

			// If not found by name, try by pageUrl
			if (!item) {
				item = mainItem.subItems.find(
					(subItem) =>
						subItem.pageUrl &&
						resolveUrl(
							resolveUrl(moreTab.pageUrl!, mainItem.pageUrl!),
							subItem.pageUrl,
						).endsWith(`/${nestedRoute}`),
				);
			}
		} else {
			item = mainItem;
		}
	}

	return item;
}

// Helper function to get feature component by route path (for More tab dynamic routing)
export function getFeatureComponent(routePath: string): React.ComponentType | undefined {
	const item = findNavigationItem(routePath);
	return item?.featureComponent;
}
// Helper function to get translation key by route path (for More tab dynamic routing)
export function getTranslationKey(routePath: string): string | undefined {
	const item = findNavigationItem(routePath);
	return item?.translationKey;
}

// Header navigation configuration for HomeStack
export const navHeaderConfig: NavigationItem[] = [
	{
		name: 'index',
		icon: 'house',
		iconVariant: 'regular',
		translationKey: 'nav_main.home',
		pageUrl: '/(tabs)/home/<USER>',
	},
	{
		name: 'create-post',
		icon: 'plus',
		iconVariant: 'regular',
		translationKey: 'nav_header.createPost',
		pageUrl: '/(tabs)/home/<USER>',
		presentation: 'modal',
	},
	{
		name: 'search',
		icon: 'magnifyingGlass',
		iconVariant: 'regular',
		translationKey: 'nav_header.search',
		pageUrl: '/(tabs)/home/<USER>',
	},
	{
		name: 'messenger',
		icon: 'comments',
		iconVariant: 'regular',
		translationKey: 'nav_header.messenger',
		pageUrl: '/(tabs)/home/<USER>',
	},
	{
		name: 'notifications',
		icon: 'bell',
		iconVariant: 'regular',
		translationKey: 'nav_header.notifications',
		pageUrl: '/(tabs)/home/<USER>',
	},
];
