/**
 * Application Routes Configuration
 *
 * Centralized route definitions to avoid hardcoded paths throughout the app.
 * Use these constants for type-safe navigation.
 */

export const ROUTES = {
	AUTH: {
		LOGIN: '/login',
		SSO_CALLBACK: '/auth',
	},
	TABS: {
		HOME: '/(tabs)',
		MORE: '/(tabs)/more',
	},
	EVENTS: {
		INDEX: '/(tabs)/more/events',
		DETAIL: (id: string) => `/(tabs)/more/events/${id}` as const,
		YOUR_EVENTS: '/(tabs)/more/events/your-events',
		GROUP_EVENTS: '/(tabs)/more/events/group-events',
		DISCOVER: '/(tabs)/more/events/discover',
		PAST: '/(tabs)/more/events/past',
		SEARCH: '/(tabs)/more/events/search',
	},
} as const;

export default ROUTES;
