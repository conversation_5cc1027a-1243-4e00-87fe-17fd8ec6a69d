/**
 * Authentication Configuration
 *
 * Centralized configuration for all authentication-related settings.
 * This serves as a single source of truth for authentication parameters
 * across the mobile app.
 */

import { Platform } from 'react-native';
import config from '@/config';
import { getOriginFromUrl } from '@/utils/urlUtils';

/**
 * Authentication endpoints configuration
 */
export interface AuthEndpoints {
	login: string;
	logout: string;
	callback: string;
	tokenExchange: string;
	userProfile: string;
}

/**
 * Complete authentication configuration
 */
export interface AuthConfig {
	// API endpoint paths
	callbackPath: string;

	// Session management
	tokenExpiryMs: number;

	// API paths (relative to base URL)
	endpoints: AuthEndpoints;

	// WebView security origins allowlist
	allowedOrigins: string[];

	// Platform-specific adjustments
	getAdjustedBaseUrl(url: string): string;

	// Get the primary allowed origin for the API
	getPrimaryAllowedOrigin(apiBaseUrl: string): string;
}

/**
 * Default authentication configuration
 */
const authConfig: AuthConfig = {
	// Callback path for WebView communication
	callbackPath: '/sso-auth/callback',

	// Session management
	tokenExpiryMs: 60 * 60 * 1000, // 1 hour by default

	// API endpoints (relative paths)
	endpoints: {
		login: '/api/auth/login',
		logout: '/api/auth/logout',
		callback: '/sso-auth/callback',
		tokenExchange: '/api/auth/exchange-token',
		userProfile: '/api/user/me',
	},

	// WebView security: allowed origins for WebView navigation
	allowedOrigins: config.WEBVIEW_ALLOWED_DOMAINS,

	// Platform-specific URL adjustments
	getAdjustedBaseUrl(baseUrl: string): string {
		// For Android emulators, replace localhost with ********
		if (Platform.OS === 'android' && baseUrl.includes('localhost')) {
			return baseUrl.replace('localhost', '********');
		}

		// For iOS, keep as is
		return baseUrl;
	},

	// Get the primary allowed origin for the API
	getPrimaryAllowedOrigin(apiBaseUrl: string): string {
		const adjustedUrl = this.getAdjustedBaseUrl(apiBaseUrl);
		return getOriginFromUrl(adjustedUrl);
	},
};

export default authConfig;
