/**
 * Navigation configuration data without component imports
 * This file contains only the data structure and can be imported by components
 * without causing circular dependencies
 */

import { NavigationItem } from './navigation.types';

// Base configuration without feature components (components will be added in navigation.ts)
export const navConfigBase: Omit<NavigationItem, 'featureComponent'>[] = [
	{
		name: 'home',
		icon: 'house',
		iconVariant: 'outline',
		translationKey: 'nav_main.home',
		pageUrl: '/(tabs)/home',
	},
	{
		name: 'profile',
		icon: 'user',
		iconVariant: 'outline',
		translationKey: 'nav_main.profile',
		pageUrl: '/(tabs)/profile',
	},
	{
		name: 'calendar',
		icon: 'calendar',
		iconVariant: 'regular',
		translationKey: 'nav_main.calendar',
		pageUrl: '/(tabs)/calendar',
	},
	{
		name: 'groups',
		icon: 'users',
		iconVariant: 'outline',
		translationKey: 'nav_main.groups',
		pageUrl: '/(tabs)/groups',
	},
	{
		name: 'more',
		icon: 'bars',
		iconVariant: 'regular',
		translationKey: 'nav_main.more',
		pageUrl: '/(tabs)/more',
		subItems: [
			{
				name: 'events',
				icon: 'calendar',
				iconVariant: 'regular',
				translationKey: 'nav_sub.events',
				pageUrl: '/(tabs)/more/events',
			},
			{
				name: 'saved',
				icon: 'bookmark',
				iconVariant: 'regular',
				translationKey: 'nav_sub.saved',
				pageUrl: '/saved',
			},
			{
				name: 'friendsNearMe',
				icon: 'map',
				iconVariant: 'regular',
				translationKey: 'nav_sub.friendsNearMe',
				pageUrl: '/friends-near-me',
			},
			{
				name: 'messenger',
				icon: 'comments',
				iconVariant: 'regular',
				translationKey: 'nav_sub.messenger',
				pageUrl: '/messenger',
			},
			{
				name: 'resourceLibrary',
				icon: 'bookOpen',
				iconVariant: 'regular',
				translationKey: 'nav_sub.resourceLibrary',
				pageUrl: '/resource-library',
			},
			{
				name: 'settings',
				icon: 'gear',
				iconVariant: 'regular',
				translationKey: 'nav_sub.settings',
				pageUrl: '/settings',
				subItems: [
					{
						name: 'notificationPreferences',
						icon: 'bell',
						iconVariant: 'regular',
						translationKey: 'nav_settings.notificationPreferences',
						pageUrl: '/notificationPreferences',
					},
					{
						name: 'privacySettings',
						icon: 'shield',
						iconVariant: 'regular',
						translationKey: 'nav_settings.privacySettings',
						pageUrl: '/privacySettings',
					},
					{
						name: 'updatePassword',
						icon: 'lock',
						iconVariant: 'regular',
						translationKey: 'nav_settings.updatePassword',
						pageUrl: '/updatePassword',
					},
					{
						name: 'managePosts',
						icon: 'comments',
						iconVariant: 'regular',
						translationKey: 'nav_settings.managePosts',
						pageUrl: '/managePosts',
					},
					{
						name: 'manageResources',
						icon: 'lightbulb',
						iconVariant: 'regular',
						translationKey: 'nav_settings.manageResources',
						pageUrl: '/manageResources',
					},
					{
						name: 'colorMode',
						icon: 'sun',
						iconVariant: 'regular',
						translationKey: 'nav_settings.colorMode',
						pageUrl: '/colorMode',
					},
				],
			},
		],
	},
];

// Footer navigation items (for external links and app actions)
export const footerNavConfig = [
	{
		name: 'communityRules',
		translationKey: 'footer.communityRules',
		url: 'https://stride.tiltedwindmillagency.com/about',
		external: true,
	},
	{
		name: 'privacy',
		translationKey: 'footer.privacy',
		url: 'https://stride.tiltedwindmillagency.com/privacy',
		external: true,
	},
	{
		name: 'termsOfService',
		translationKey: 'footer.termsOfService',
		url: 'https://stride.tiltedwindmillagency.com/terms',
		external: true,
	},
	{
		name: 'signOut',
		translationKey: 'auth.signOut',
		action: 'signOut',
	},
	{
		name: 'copyright',
		translationKey: 'footer.copyright',
		action: 'showCopyright',
	},
];

// Header navigation configuration for HomeStack
export const navHeaderConfig: Omit<NavigationItem, 'featureComponent'>[] = [
	{
		name: 'index',
		icon: 'house',
		iconVariant: 'outline',
		translationKey: 'nav_main.home',
		pageUrl: '/(tabs)/home/<USER>',
	},
	{
		name: 'create-post',
		icon: 'plus',
		iconVariant: 'regular',
		translationKey: 'nav_header.createPost',
		pageUrl: '/(tabs)/home/<USER>',
		presentation: 'modal',
	},
	{
		name: 'search',
		icon: 'magnifyingGlass',
		iconVariant: 'regular',
		translationKey: 'nav_header.search',
		pageUrl: '/(tabs)/home/<USER>',
	},
	{
		name: 'messenger',
		icon: 'comments',
		iconVariant: 'regular',
		translationKey: 'nav_header.messenger',
		pageUrl: '/(tabs)/home/<USER>',
	},
	{
		name: 'notifications',
		icon: 'bell',
		iconVariant: 'regular',
		translationKey: 'nav_header.notifications',
		pageUrl: '/(tabs)/home/<USER>',
	},
];
