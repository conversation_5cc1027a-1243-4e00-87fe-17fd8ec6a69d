/**
 * Navigation types and configuration data
 * Separated from navigation.ts to avoid circular dependencies
 */

import React from 'react';

export interface NavigationItem {
	name: string;
	icon: string;
	iconVariant?: 'fill' | 'regular' | 'outline';
	translationKey: string; // Must exist in translation files
	pageUrl?: string; // Optional - some items may not have direct pages
	subItems?: NavigationItem[]; // For hierarchical navigation
	presentation?: 'modal' | 'card'; // For stack screen presentation (defaults to 'card'/page view if not specified)
	featureComponent?: React.ComponentType; // Feature component for dynamic routing
}

// Helper function to resolve relative URLs for subitems
export function resolveUrl(parentUrl: string, childUrl: string): string {
	// If child URL contains Expo route syntax or is external, treat as absolute
	if (childUrl.includes('/(') || childUrl.startsWith('http')) {
		return childUrl;
	}
	// If child URL starts with '/', treat as relative to parent
	if (childUrl.startsWith('/')) {
		return parentUrl + childUrl;
	}
	// Otherwise, assume it's absolute
	return childUrl;
}
