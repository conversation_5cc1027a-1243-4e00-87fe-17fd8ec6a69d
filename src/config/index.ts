import appConfig from '../../app.config.json';
import { getOriginFromUrl } from '@/utils/urlUtils';

export interface EnvConfig {
	ENV_NAME: string;
	API_URL: string; // from .env
	X_PUBLIC_KEY: string; // from .env
	DEBUG: boolean; // from .env
	AUTH_REDIRECT_URL: string; // from app.config.json
	CONFIG_VERSION: string; // from app.config.json
	FEATURE_X_ENABLED: boolean; // from app.config.json
	WEBVIEW_ALLOWED_DOMAINS: string[]; // from app.config.json
}

const getEnv = (): EnvConfig => {
	const extra = process.env || {};

	// Get API URL from environment or use fallback
	const apiUrl = extra.EXPO_PUBLIC_API_URL ?? 'https://fallback-api.example.com';

	// Get base domains from config
	const configuredDomains = appConfig.WEBVIEW_ALLOWED_DOMAINS || [];

	// Extract origin from API URL and format as wildcard
	const apiOrigin = getOriginFromUrl(apiUrl);
	const apiDomain = apiOrigin ? `${apiOrigin}/*` : '';

	// Add API domain to allowed domains if not already included
	const allowedDomains = apiDomain
		? [...configuredDomains, apiDomain].filter(
				(domain, index, self) => self.indexOf(domain) === index,
			)
		: configuredDomains;

	return {
		ENV_NAME: extra.EXPO_PUBLIC_ENV_NAME ?? 'NO FILE!',
		API_URL: apiUrl,
		X_PUBLIC_KEY: extra.EXPO_PUBLIC_X_PUBLIC_KEY ?? '',
		DEBUG: extra.EXPO_PUBLIC_DEBUG === 'true',
		AUTH_REDIRECT_URL: appConfig.AUTH_REDIRECT_URL ?? 'learningcoachcommunity://auth-callback',
		CONFIG_VERSION: appConfig.CONFIG_VERSION ?? '0.0.0',
		FEATURE_X_ENABLED: appConfig.FEATURE_X_ENABLED ?? false,
		WEBVIEW_ALLOWED_DOMAINS: allowedDomains,
	};
};

export default getEnv();
