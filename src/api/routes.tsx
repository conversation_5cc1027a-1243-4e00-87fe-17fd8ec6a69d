import { ApiMethods } from '@/types';

// Valid platform types for authentication
export type PlatformType = 'mobile' | 'web' | 'desktop';

export const Routes = {
	user: {
		me: () => ({
			path: `/user`,
			method: ApiMethods.GET,
			authenticated: true,
		}),
	},
	auth: {
		devLogin: (email: string) => ({
			path: '/dev-login',
			method: ApiMethods.POST,
			body: { email: email },
			authenticated: false,
		}),
		exchangeToken: () => ({
			path: '/exchange-token',
			method: ApiMethods.POST,
			authenticated: false,
		}),
		logout: () => ({
			path: '/logout',
			method: ApiMethods.POST,
			authenticated: true,
		}),
		/**
		 * WebView-only route: Returns a login page URL for OAuth/SSO authentication.
		 * This is NOT a JSON API endpoint - it returns HTML for display in a WebView.
		 * Used by AuthService.getLoginUrl() for SSO middleware authentication flow.
		 * @param platform - The platform type ('mobile', 'web', 'desktop')
		 * @param state - Optional state parameter for auth flow
		 */
		login: (platform: PlatformType = 'mobile', state?: string) => {
			// Validate platform parameter
			if (platform !== 'mobile') {
				//} && platform !== 'web' && platform !== 'desktop') {
				throw new Error(
					`Invalid platform: ${platform}. Must be 'mobile',`, // 'web', or 'desktop'`,
				);
			}

			return {
				path: '/auth/login',
				method: ApiMethods.GET,
				authenticated: false,
				// Query params for WebView authentication flow
				queryParams: {
					platform,
					...(state ? { state } : {}),
				},
			};
		},
	},
	posts: {
		fetch: () => ({ path: '/posts/timeline', method: ApiMethods.GET, authenticated: true }),
		post: (title: string, content: string) => ({
			path: '/posts/create',
			method: ApiMethods.POST,
			body: { title: title, text: content },
			authenticated: true,
		}),
	},
	events: {
		// TODO: confirm types here ... is this how we handle 'discover'?
		fetch: (type: 'previous' | 'current' | 'upcoming' | 'discover' = 'current') => ({
			path: `/events/${type}`,
			method: ApiMethods.GET,
			authenticated: true,
		}),
		getDetails: (eventId: string) => ({
			path: `/events/${eventId}`,
			method: ApiMethods.GET,
			authenticated: true,
		}),
		updateStatus: (eventId: string, status: 'going' | 'interested' | 'not_going') => ({
			path: `/events/${eventId}/status`,
			method: ApiMethods.POST,
			body: { status },
			authenticated: true,
		}),
	},
} as const;

export type RouteTree = typeof Routes;

export type RouteQueryParams = Record<string, string | number | boolean>;

export type RouteDef =
	| {
			path: string;
			method: ApiMethods;
			body?: any;
			authenticated?: boolean;
			queryParams?: RouteQueryParams;
	  }
	| ((...args: any[]) => {
			path: string;
			method: ApiMethods;
			body?: any;
			authenticated?: boolean;
			queryParams?: RouteQueryParams;
	  });

/**
 * Build a complete URL from a route definition, including query parameters
 */
export const buildRouteUrl = (baseUrl: string, route: RouteDef, ...args: any[]): string => {
	const resolved = typeof route === 'function' ? route(...args) : route;
	let url = `${baseUrl.replace(/\/$/, '')}${resolved.path}`;

	if (resolved.queryParams) {
		const searchParams = new URLSearchParams();
		Object.entries(resolved.queryParams).forEach(([key, value]) => {
			if (value !== undefined && value !== null) {
				searchParams.append(key, String(value));
			}
		});
		const queryString = searchParams.toString();
		if (queryString) {
			url += `?${queryString}`;
		}
	}

	return url;
};

export type Id = string | number;
