/**
 * API Service
 *
 * Centralized API client using dependency injection for configuration
 * Follows CLEAN architecture principles with injected dependencies
 */

import { type EnvConfig } from '@/config';
import { secureStorageRepository } from '@/repositories/secureStorageRepository';
import type {
	ISecureStorageRepository,
	IApiConfigService,
	ApiClientOptions,
	ApiResponse,
} from '@/types';
import type { RouteDef } from '@/api/routes';
import { buildRouteUrl } from '@/api/routes';
// Platform-specific normalization is handled by the injected configService

/**
 * API Error class
 */
class ApiError extends Error {
	constructor(
		message: string,
		public status?: number,
		public code?: string,
	) {
		super(message);
		this.name = 'ApiError';
	}
}

/**
 * API Client class with dependency injection
 */
class ApiClient {
	private _baseUrl: string;
	private timeout: number;
	private retryAttempts: number;
	private config: EnvConfig;
	private storageRepository: ISecureStorageRepository;
	private configService: IApiConfigService;

	constructor(
		config: EnvConfig,
		storageRepository: ISecureStorageRepository = secureStorageRepository,
		configService: IApiConfigService,
		options: ApiClientOptions = {},
	) {
		this.config = config;
		this.storageRepository = storageRepository;
		this.configService = configService;
		this._baseUrl = options.baseUrl || config.API_URL;
		this.timeout = options.timeout ?? 10000; // Default timeout in milliseconds
		this.retryAttempts = options.retryAttempts ?? 3; // Default retry attempts

		if (config.DEBUG) {
			console.log('🌐 API Client initialized:', {
				baseUrl: this.baseUrl,
				timeout: this.timeout,
				retryAttempts: this.retryAttempts,
			});
		}
	}

	/**
	 * Get the base URL for API requests with platform-specific adjustments
	 */
	get baseUrl(): string {
		const raw = this._baseUrl;
		const normalized =
			typeof this.configService.normalizeBaseUrl === 'function'
				? this.configService.normalizeBaseUrl(raw)
				: raw;
		return normalized;
	}

	/**
	 * Get authentication headers
	 */
	private async getAuthHeaders(): Promise<Record<string, string>> {
		try {
			// Use injected storage repository for proper dependency injection
			const authData = await this.storageRepository.getAuthData();
			if (authData && typeof authData === 'object' && authData.token) {
				const token = String(authData.token).trim();
				if (token) {
					return { Authorization: `Bearer ${token}` };
				}
			}
			return {};
		} catch {
			return {};
		}
	}

	/**
	 * Make HTTP request with retry logic
	 */
	private async makeRequest<T>(
		endpoint: string,
		options: RequestInit = {},
		attempt: number = 1,
	): Promise<ApiResponse<T>> {
		const url = new URL(endpoint, this.baseUrl).toString();

		try {
			// Debug logging
			if (this.config && (this.config as any).DEBUG) {
				console.log('🌐 API Request URL:', url);
			}

			const authHeaders = await this.getAuthHeaders();

			// Prepare request
			const controller = new AbortController();
			const timeoutId = setTimeout(() => controller.abort(), this.timeout);

			const response = await fetch(url, {
				...options,
				credentials: 'include', // Include cookies in cross-origin requests
				headers: (() => {
					// Build headers and only set Content-Type when appropriate
					const computed: Record<string, any> = {
						...authHeaders,
						...options.headers,
					};
					// Ensure Accept header defaults to JSON so server treats it as an API request
					const hasAccept = Object.keys(computed).some(
						(k) => k.toLowerCase() === 'accept',
					);
					if (!hasAccept) {
						computed['Accept'] = 'application/json';
					}
					const hasContentType = Object.keys(computed).some(
						(k) => k.toLowerCase() === 'content-type',
					);
					if (
						!hasContentType &&
						(options as any).body &&
						!((options as any).body instanceof FormData)
					) {
						computed['Content-Type'] = 'application/json';
					}
					return computed;
				})(),
				signal: controller.signal,
			});

			clearTimeout(timeoutId);

			// Handle response
			if (!response.ok) {
				throw new ApiError(
					`HTTP ${response.status}: ${response.statusText}`,
					response.status,
				);
			}

			const data = await response.json();
			return {
				data,
				success: true,
			};
		} catch (error: any) {
			// Retry logic (avoid retrying on auth errors and certain endpoints)
			if (attempt < this.retryAttempts && this.shouldRetry(error, url)) {
				if (this.config && (this.config as any).DEBUG) {
					console.warn(
						`API request failed, retrying (${attempt}/${this.retryAttempts}):`,
						error,
					);
				}
				await this.delay(1000 * attempt); // Simple exponential backoff
				return this.makeRequest<T>(endpoint, options, attempt + 1);
			}

			// Handle different error types - final normalization
			if (error instanceof ApiError) {
				throw error;
			}

			if (error && error.name === 'AbortError') {
				throw new ApiError('Request timeout', 408, 'TIMEOUT');
			}

			throw new ApiError((error && error.message) || 'Network error', 0, 'NETWORK_ERROR');
		}
	}

	/**
	 * Determine if request should be retried
	 */
	private shouldRetry(error: any, url: string): boolean {
		// Never retry timeouts
		if (error && error.name === 'AbortError') return false;
		// Never retry auth errors
		if (error instanceof ApiError && (error.status === 401 || error.status === 403)) {
			return false;
		}
		// Avoid retrying certain endpoints on 5xx (e.g., user profile or logout)
		if (error instanceof ApiError && (error.status ?? 0) >= 500) {
			try {
				const u = new URL(url);
				if (u.pathname.endsWith('/user') || u.pathname.endsWith('/logout')) return false;
			} catch {
				// If URL parsing fails, fall back to checking substring
				if (url.endsWith('/user') || url.endsWith('/logout')) return false;
			}
		}
		// Retry on 5xx or unknown (status 0) for other endpoints
		if (error instanceof ApiError) {
			return (error.status ?? 0) >= 500 || (error.status ?? 0) === 0;
		}
		// Retry generic network errors
		return true;
	}

	/**
	 * Delay utility for retry logic
	 */
	private delay(ms: number): Promise<void> {
		return new Promise((resolve) => setTimeout(resolve, ms));
	}

	/**
	 * Generic request method using RouteDef
	 */
	async request<T>(route: RouteDef, body: any = null): Promise<ApiResponse<T>> {
		// Resolve the route if it's a function
		const resolvedRoute = typeof route === 'function' ? route() : route;
		const { method, body: routeBody } = resolvedRoute as any;
		const requestBody = body ?? routeBody;

		const options: RequestInit = {
			method,
			body:
				requestBody != null && Object.keys(requestBody).length > 0
					? JSON.stringify(requestBody)
					: undefined,
		};

		// Always build the full URL using buildRouteUrl for consistent normalization
		// Use the baseUrl getter to ensure platform-specific adjustments are applied
		const url = buildRouteUrl(this.baseUrl, resolvedRoute as RouteDef);
		return this.makeRequest<T>(url, options);
	}

	/**
	 * GET request with query parameters
	 * @param endpoint - API endpoint path
	 * @param params - Optional query parameters
	 */
	async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
		let url = endpoint;

		if (params && Object.keys(params).length > 0) {
			// Create URLSearchParams instance for proper URL encoding
			const searchParams = new URLSearchParams();

			// Add each parameter if it's not undefined or null
			Object.entries(params).forEach(([key, value]) => {
				// Skip undefined and null values
				if (value !== undefined && value !== null) {
					if (Array.isArray(value)) {
						value.forEach((item) => {
							if (item !== undefined && item !== null) {
								searchParams.append(key, String(item));
							}
						});
					} else {
						searchParams.append(key, String(value));
					}
				}
			});

			// Only append query string if we have parameters
			const queryString = searchParams.toString();
			if (queryString) {
				url += `?${queryString}`;
			}
		}

		return this.makeRequest<T>(url, { method: 'GET' });
	}

	/**
	 * POST request
	 */
	async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
		return this.makeRequest<T>(endpoint, {
			method: 'POST',
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	/**
	 * PUT request
	 */
	async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
		return this.makeRequest<T>(endpoint, {
			method: 'PUT',
			body: data ? JSON.stringify(data) : undefined,
		});
	}

	/**
	 * DELETE request
	 */
	async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
		return this.makeRequest<T>(endpoint, { method: 'DELETE' });
	}

	/**
	 * Upload file
	 */
	async upload<T>(
		endpoint: string,
		file: File | Blob,
		additionalData?: Record<string, any>,
	): Promise<ApiResponse<T>> {
		const formData = new FormData();
		formData.append('file', file);

		if (additionalData) {
			Object.entries(additionalData).forEach(([key, value]) => {
				formData.append(key, String(value));
			});
		}

		const authHeaders = await this.getAuthHeaders();

		return this.makeRequest<T>(endpoint, {
			method: 'POST',
			body: formData,
			headers: {
				// Don't set Content-Type for FormData - let browser set it with boundary
				...authHeaders,
			},
		});
	}
}

// Export the ApiClient class for custom instantiation
export { ApiClient };

// Export error class for error handling
export { ApiError };

// Export types
export type { ApiResponse };
