/**
 * Centralized Error Handler
 *
 * Provides standardized error handling for API calls and authentication flows.
 * This ensures consistent error messages across the app and proper cleanup
 * of authentication state when auth errors occur.
 */

import { secureStorageRepository } from '@/repositories/secureStorageRepository';

/**
 * Standard API error structure
 */
export interface ApiError {
	message: string;
	code?: string;
	status?: number;
	originalError?: unknown;
}

/**
 * Transforms any error into a standardized API error
 */
export function normalizeError(error: unknown): ApiError {
	if (error instanceof Error) {
		return {
			message: error.message,
			originalError: error,
		};
	}

	// Handle string errors
	if (typeof error === 'string') {
		return { message: error };
	}

	// Handle unknown error types
	return {
		message: 'An unknown error occurred',
		originalError: error,
	};
}

/**
 * Extract HTTP status code from error message if present
 */
export function extractHttpStatus(errorMessage: string): number | null {
	const match = errorMessage.match(/HTTP (\d+)/i);
	return match ? parseInt(match[1], 10) : null;
}

/**
 * Handle authentication-related errors
 * Returns a user-friendly error message and performs necessary cleanup
 */
export async function handleAuthError(error: unknown): Promise<string> {
	const apiError = normalizeError(error);
	const errorMessage = apiError.message;
	const status = extractHttpStatus(errorMessage);

	if (status === 401 || errorMessage.includes('HTTP 401')) {
		try {
			// Clear invalid/expired token to avoid loops
			await secureStorageRepository.clearAuthData();
		} catch {}
		return 'Authentication failed. Please log in again.';
	}

	if (status === 500 || errorMessage.includes('HTTP 500')) {
		// Some environments may return 500 instead of 401 when unauthenticated
		try {
			await secureStorageRepository.clearAuthData();
		} catch {}
		return 'Authentication failed. Please log in again.';
	}

	if (status === 403 || errorMessage.includes('HTTP 403')) {
		return "You don't have permission to access this resource.";
	}

	if (errorMessage.includes('Network Error') || errorMessage.includes('Failed to fetch')) {
		return 'Network error. Please check your connection and try again.';
	}

	// Default error message
	return 'An unexpected error occurred: ' + errorMessage;
}

/**
 * Handle API errors (general)
 */
export function handleApiError(error: unknown): string {
	const apiError = normalizeError(error);

	if (__DEV__) {
		console.warn('[ErrorHandler] API error:', apiError.message);
	}

	if (
		apiError.message.includes('Network Error') ||
		apiError.message.includes('Failed to fetch')
	) {
		return 'Network error. Please check your connection and try again.';
	}

	return 'API request failed: ' + apiError.message;
}

/**
 * Log error details (dev only)
 */
export function logError(context: string, error: unknown): void {
	if (__DEV__) {
		const apiError = normalizeError(error);
		console.error(`[${context}] Error:`, apiError.message, apiError.originalError || '');
	}
}

export default {
	normalizeError,
	handleAuthError,
	handleApiError,
	logError,
	extractHttpStatus,
};
