/**
 * Logger Utility
 *
 * Centralized logging system with different log levels for better debugging
 * and cleaner console output. Only logs in development mode.
 */

interface LoggerConfig {
	enableDebug?: boolean;
	enableInfo?: boolean;
	prefix?: string;
}

class Logger {
	private config: LoggerConfig;

	constructor(config: LoggerConfig = {}) {
		this.config = {
			enableDebug: __DEV__,
			enableInfo: __DEV__,
			...config,
		};
	}

	/**
	 * Debug level - for detailed debugging information
	 */
	debug(message: string, ...args: any[]) {
		if (this.config.enableDebug && __DEV__) {
			const prefix = this.config.prefix ? `[${this.config.prefix}]` : '[DEBUG]';
			console.debug(`${prefix} ${message}`, ...args);
		}
	}

	/**
	 * Info level - for general information
	 */
	info(message: string, ...args: any[]) {
		if (this.config.enableInfo && __DEV__) {
			const prefix = this.config.prefix ? `[${this.config.prefix}]` : '[INFO]';
			console.log(`${prefix} ${message}`, ...args);
		}
	}

	/**
	 * Warning level - for warnings that don't break functionality
	 */
	warn(message: string, ...args: any[]) {
		if (__DEV__) {
			const prefix = this.config.prefix ? `[${this.config.prefix}]` : '[WARN]';
			console.warn(`${prefix} ${message}`, ...args);
		}
	}

	/**
	 * Error level - for errors and exceptions
	 */
	error(message: string, ...args: any[]) {
		const prefix = this.config.prefix ? `[${this.config.prefix}]` : '[ERROR]';
		console.error(`${prefix} ${message}`, ...args);
	}

	/**
	 * Create a child logger with a specific prefix
	 */
	child(prefix: string): Logger {
		return new Logger({
			...this.config,
			prefix: this.config.prefix ? `${this.config.prefix}:${prefix}` : prefix,
		});
	}
}

// Export default logger instance
export const logger = new Logger();

// Export logger class for creating custom instances
export default Logger;
