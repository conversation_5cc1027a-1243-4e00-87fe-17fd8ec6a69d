// SHADOWS - composite tokens for shadow styling properties
export const Shadows: ShadowProps = {
	xs: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 1 },
		shadowOpacity: 0.05,
		shadowRadius: 2,
		elevation: 2,
	},
	sm: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.1,
		shadowRadius: 3,
		elevation: 3,
	},
	md: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 4 },
		shadowOpacity: 0.1,
		shadowRadius: 6,
		elevation: 4,
	},
	lg: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 8 },
		shadowOpacity: 0.1,
		shadowRadius: 12,
		elevation: 8,
	},
	xl: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 12 },
		shadowOpacity: 0.15,
		shadowRadius: 24,
		elevation: 12,
	},
	xxl: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 24 },
		shadowOpacity: 0.15,
		shadowRadius: 48,
		elevation: 24,
	},
	xxxl: {
		shadowColor: '#0A0D12',
		shadowOffset: { width: 0, height: 32 },
		shadowOpacity: 0.15,
		shadowRadius: 64,
		elevation: 32,
	},
};

type ShadowStyles = {
	shadowColor: string;
	shadowOffset: { width: number; height: number };
	shadowOpacity: number;
	shadowRadius: number;
	elevation: number;
};

type ShadowProps = {
	xs: ShadowStyles;
	sm: ShadowStyles;
	md: ShadowStyles;
	lg: ShadowStyles;
	xl: ShadowStyles;
	xxl: ShadowStyles;
	xxxl: ShadowStyles;
};

// LINEAR GRADIENTS - composite tokens for expo's <LinearGradient> component
export const Gradients = {
	whiteLight: {
		colors: ['#FAFAFA', '#FFFFFF'],
		start: { x: 1, y: 1 },
		end: { x: 1, y: 0 },
	},
	whiteMedium: {
		colors: ['#F5F5F5', '#FFFFFF'],
		start: { x: 1, y: 1 },
		end: { x: 1, y: 0 },
	},
	whiteDark: {
		colors: ['#E9EAEB', '#F5F5F5'],
		start: { x: 1, y: 1 },
		end: { x: 1, y: 0 },
	},
	blackLight: {
		colors: ['#535862', '#717680'],
		start: { x: 0, y: 0 },
		end: { x: 1, y: 0 },
	},
	blackMedium: {
		colors: ['#252B37', '#535862'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
	blackDark: {
		colors: ['#181D27', '#414651'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
	brandLight: {
		colors: ['#1570EF', '#2E90FA'],
		start: { x: 0, y: 0 },
		end: { x: 1, y: 0 },
	},
	brandMedium: {
		colors: ['#1849A9', '#1570EF'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
	brandDark: {
		colors: ['#194185', '#175CD3'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
	warningYellow: {
		colors: ['#FFB800', '#FFF500'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
	errorOrange: {
		colors: ['#FF7A00', '#FFD439'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
	errorRed: {
		colors: ['#F49062', '#FD371F'],
		start: { x: 0, y: 0 },
		end: { x: 1, y: 1 },
	},
	successLimeGreen: {
		colors: ['#96FBC4', '#F9F586'],
		start: { x: 0, y: 1 },
		end: { x: 0, y: 0 },
	},
	successGreen: {
		colors: ['#4DEF8E', '#FFEB3A'],
		start: { x: 0, y: 1 },
		end: { x: 1, y: 0 },
	},
} as const;
