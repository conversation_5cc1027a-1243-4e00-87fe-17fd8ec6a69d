import { useTheme as useNavTheme, Theme as NavTheme } from '@react-navigation/native';
import { fonts } from './fonts';
import { ColorMode, ColorModeProps } from '@/theme/colors';
import { TokensProps, Tokens as tokensConfig } from '@/theme/tokens';
export type AppTheme = Omit<NavTheme, 'colors'> & {
	colors: NavTheme['colors'] & ColorModeProps;
	tokens: TokensProps;
};

export const DefaultTheme: AppTheme = {
	dark: false,
	colors: ColorMode.light,
	tokens: tokensConfig,
	fonts,
};

export function useTheme(): AppTheme {
	return useNavTheme<AppTheme>();
}

export { Tokens } from '@/theme/tokens';
export { Colors } from '@/theme/colors';
export { FontSizes, FontStyles } from '@/theme/fonts';
export { Shadows, Gradients } from '@/theme/effects';
