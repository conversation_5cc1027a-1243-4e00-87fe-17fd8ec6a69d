/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

// COLOR PALETTE - base color tokens
export const COLOR_PALETTE = {
	white: '#ffffff',
	black: '#141414',
	uiBlue: '#1548E6',
	gray: {
		100: '#F9FAFB',
		150: '#F2F4F7',
		200: '#E4E7EC',
		250: '#D0D5DD',
		300: '#BDC4CF',
		400: '#9DA4AF',
		500: '#7B7E89',
		600: '#555762',
		700: '#555762',
		800: '#27282E',
	},
	red: {
		100: '#FFE0E4',
		200: '#F3B5C8',
		300: '#FD6F8E',
		400: '#DB1B43',
		500: '#E10B02',
		600: '#9E0D00',
		700: '#780800',
		800: '#590000',
		900: '#2E0000',
	},
	orange: {
		100: '#FFECE6',
		200: '#FED4BE',
		300: '#F7AF79',
		400: '#FD762B',
		500: '#DB4F23',
		600: '#8C2F07',
		700: '#682002',
		800: '#521600',
		900: '#3B0A00',
	},
	yellow: {
		100: '#FFFCE5',
		200: '#FCF3BE',
		300: '#FDEF72',
		400: '#FFEA2E',
		500: '#FFCC00',
		600: '#EFAE06',
		700: '#713B12',
		800: '#442001',
		900: '#2D1200',
	},
	green: {
		100: '#EEFCEF',
		200: '#C0F3C2',
		300: '#8EE68C',
		400: '#6AD051',
		500: '#1AA60D',
		600: '#096F00',
		700: '#084C2E',
		800: '#07301C',
		900: '#022011',
	},
	cyan: {
		100: '#E4F8FF',
		200: '#CFF9FE',
		300: '#A5F0FC',
		400: '#67E3F9',
		500: '#16AFDF',
		600: '#0A789A',
		700: '#045770',
		800: '#003E52',
		900: '#002C3C',
	},
	blue: {
		100: '#E8F6FC',
		200: '#CFEAFE',
		300: '#A2DEFF',
		400: '#62C7FF',
		500: '#0035F0',
		600: '#0029B9',
		700: '#01308E',
		800: '#142463',
		900: '#0C1544',
	},
	purple: {
		100: '#FBE8FF',
		200: '#F6D0FE',
		300: '#EEAAFD',
		400: '#F380FE',
		500: '#BA07CF',
		600: '#9207A2',
		700: '#6F1877',
		800: '#4A094C',
		900: '#2E052F',
	},
};

// COLOR THEME - text, border, foreground, and background color tokens
export const Colors = {
	text: {
		primary: COLOR_PALETTE.gray[800],
		primaryOnBrand: COLOR_PALETTE.white,
		secondary: COLOR_PALETTE.gray[700],
		secondaryOnBrand: COLOR_PALETTE.blue[100],
		secondaryHover: COLOR_PALETTE.gray[800],
		tertiary: COLOR_PALETTE.gray[600],
		tertiaryOnBrand: COLOR_PALETTE.blue[100],
		tertiaryHover: COLOR_PALETTE.gray[800],
		quaternary: COLOR_PALETTE.gray[500],
		quaternaryOnBrand: COLOR_PALETTE.blue[200],
		white: COLOR_PALETTE.white,
		disabled: COLOR_PALETTE.gray[400],
		placeholder: COLOR_PALETTE.gray[500],
		placeholderSubtle: COLOR_PALETTE.gray[300],
		brand: COLOR_PALETTE.uiBlue,
		errorPrimary: COLOR_PALETTE.red[500],
		warningPrimary: COLOR_PALETTE.orange[400],
		successPrimary: COLOR_PALETTE.green[600],
	},
	border: {
		primary: COLOR_PALETTE.gray[300],
		secondary: COLOR_PALETTE.gray[200],
		tertiary: COLOR_PALETTE.gray[100],
		disabled: COLOR_PALETTE.gray[300],
		disabledSubtle: COLOR_PALETTE.gray[200],
		brand: COLOR_PALETTE.uiBlue,
		brantAlt: COLOR_PALETTE.blue[400],
		error: COLOR_PALETTE.red[500],
		errorSubtle: COLOR_PALETTE.red[100],
	},
	fg: {
		primary: COLOR_PALETTE.gray[800],
		secondary: COLOR_PALETTE.gray[700],
		secondaryHover: COLOR_PALETTE.gray[800],
		tertiary: COLOR_PALETTE.gray[600],
		tertiaryHover: COLOR_PALETTE.gray[700],
		quaternary: COLOR_PALETTE.gray[500],
		quaternaryHover: COLOR_PALETTE.gray[600],
		quinary: COLOR_PALETTE.gray[400],
		quinaryHover: COLOR_PALETTE.gray[500],
		senary: COLOR_PALETTE.gray[300],
		white: COLOR_PALETTE.white,
		disabled: COLOR_PALETTE.gray[400],
		disabledSubtle: COLOR_PALETTE.gray[300],
		brandPrimary: COLOR_PALETTE.uiBlue,
		brandPrimaryAlt: COLOR_PALETTE.blue[600],
		errorPrimary: COLOR_PALETTE.red[500],
		errorSecondary: COLOR_PALETTE.red[400],
		warningPrimary: COLOR_PALETTE.orange[500],
		warningSecondary: COLOR_PALETTE.orange[400],
		successPrimary: COLOR_PALETTE.green[600],
		successSecondary: COLOR_PALETTE.green[500],
	},
	bg: {
		primary: COLOR_PALETTE.white,
		primaryAlt: COLOR_PALETTE.white,
		primaryHover: COLOR_PALETTE.gray[150],
		primarySolid: COLOR_PALETTE.gray[800],
		secondary: COLOR_PALETTE.gray[150],
		secondarySolid: COLOR_PALETTE.gray[600],
		tertiary: COLOR_PALETTE.gray[100],
		quaternary: COLOR_PALETTE.gray[200],
		active: COLOR_PALETTE.blue[100],
		disabled: COLOR_PALETTE.gray[100],
		disabledSubtle: COLOR_PALETTE.gray[150],
		overlay: COLOR_PALETTE.gray[500],
		brandPrimary: COLOR_PALETTE.blue[100],
		brandSecondary: COLOR_PALETTE.blue[200],
		brandSolid: COLOR_PALETTE.uiBlue,
		brandSolidHover: COLOR_PALETTE.blue[700],
		brandSection: COLOR_PALETTE.blue[600],
		errorPrimary: COLOR_PALETTE.red[100],
		errorSecondary: COLOR_PALETTE.orange[100],
		errorSolid: COLOR_PALETTE.red[500],
		warningPrimary: COLOR_PALETTE.yellow[100],
		warningSecondary: COLOR_PALETTE.orange[200],
		warningSolid: COLOR_PALETTE.orange[500],
		successPrimary: COLOR_PALETTE.green[100],
		successSecondary: COLOR_PALETTE.green[200],
		successSolid: COLOR_PALETTE.green[500],
	},
	blur: {
		light: 'rgba(255, 255, 255, 0.60)',
		dark: 'rgba(0, 0, 0, 0.60)',
	},
};

//COLOR MODE - light and dark theme tokens
export type ColorModeProps = {
	text: string;
	primary: string;
	background: string;
	card: string;
	tint: string;
	icon: string;
	tabIconDefault: string;
	tabIconSelected: string;
	border: string;
	notification: string;
};

export type ColorModeConfig = {
	light: ColorModeProps;
	dark: ColorModeProps;
};

export const ColorMode: ColorModeConfig = {
	light: {
		text: Colors.text.tertiary,
		primary: Colors.text.brand,
		background: Colors.bg.primary,
		card: Colors.bg.secondary,
		tint: Colors.fg.brandPrimary,
		icon: Colors.text.tertiary,
		tabIconDefault: Colors.fg.brandPrimary,
		tabIconSelected: Colors.fg.brandPrimaryAlt,
		border: Colors.border.primary,
		notification: Colors.text.errorPrimary,
	},
	dark: {
		text: Colors.text.white,
		primary: Colors.text.primaryOnBrand,
		background: Colors.bg.primarySolid,
		card: Colors.bg.secondarySolid,
		tint: Colors.fg.brandPrimaryAlt,
		icon: Colors.text.tertiary,
		tabIconDefault: Colors.fg.brandPrimary,
		tabIconSelected: Colors.fg.brandPrimaryAlt,
		border: Colors.border.primary,
		notification: Colors.text.errorPrimary,
	},
};
