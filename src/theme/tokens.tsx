export const Tokens: TokensProps = {
	spacing: {
		xs: 4,
		sm: 8,
		md: 16,
		lg: 24,
		xl: 32,
	},
	radius: {
		xxs: 2,
		xs: 4,
		sm: 8,
		md: 12,
		lg: 16,
		xl: 20,
		xxl: 24,
		xxxl: 32,
		xxxxl: 48,
		xxxxxl: 200,
	},
	sizes: {
		xxs: 8,
		xs: 12,
		sm: 16,
		md: 32,
		lg: 64,
		xl: 128,
		icon: 24,
	},
	blur: {
		sm: 8,
		md: 12,
		lg: 24,
		xl: 40,
	},
};

type SizesProps = {
	xxs: number; // extra extra small
	xs: number; // extra small
	sm: number; // small
	md: number; // medium
	lg: number; // large
	xl: number; // extra large
	icon: number; // zamiast tabBarIconSize, bardziej uniwersalnie
};

type SpacingProps = {
	xs: number;
	sm: number;
	md: number;
	lg: number;
	xl: number;
};

type RadiusProps = {
	xxs: number;
	xs: number;
	sm: number;
	md: number;
	lg: number;
	xl: number;
	xxl: number;
	xxxl: number;
	xxxxl: number;
	xxxxxl: number;
};

type BlurProps = {
	sm: number;
	md: number;
	lg: number;
	xl: number;
};

export type TokensProps = {
	spacing: SpacingProps;
	radius: RadiusProps;
	sizes: SizesProps;
	blur: BlurProps;
};
