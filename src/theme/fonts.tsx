import { Theme } from '@react-navigation/native';
import { Platform, TextStyle } from 'react-native';

const WEB_FONT_STACK =
	'PoppinsRegular, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol"';

export const fonts = Platform.select({
	web: {
		regular: {
			fontFamily: WEB_FONT_STACK,
			fontWeight: '400',
		},
		medium: {
			fontFamily: WEB_FONT_STACK,
			fontWeight: '500',
		},
		bold: {
			fontFamily: WEB_FONT_STACK,
			fontWeight: '600',
		},
		heavy: {
			fontFamily: WEB_FONT_STACK,
			fontWeight: '700',
		},
	},
	ios: {
		regular: {
			fontFamily: 'Poppins',
			fontWeight: '400',
		},
		medium: {
			fontFamily: 'Poppins',
			fontWeight: '500',
		},
		bold: {
			fontFamily: 'Poppins',
			fontWeight: '600',
		},
		heavy: {
			fontFamily: 'Poppins',
			fontWeight: '700',
		},
	},
	default: {
		regular: {
			fontFamily: 'PoppinsRegular',
			fontWeight: 'normal',
		},
		medium: {
			fontFamily: 'PoppinsMedium',
			fontWeight: 'normal',
		},
		bold: {
			fontFamily: 'PoppinsSemiBold',
			fontWeight: 'normal',
		},
		heavy: {
			fontFamily: 'PoppinsBold',
			fontWeight: 'normal',
		},
	},
} as const satisfies Record<string, Theme['fonts']>);

// FONT STYLES - tokens for styles like weight, italic, uppercase, and underline
export const FontStyles: FontStyles = {
	regular: {
		fontFamily: 'PoppinsRegular',
		fontWeight: 'normal',
	},
	italic: {
		fontFamily: 'PoppinsItalic',
		fontWeight: 'normal',
	},
	medium: {
		fontFamily: 'PoppinsMedium',
		fontWeight: 'normal',
	},
	mediumItalic: {
		fontFamily: 'PoppinsMediumItalic',
		fontWeight: 'normal',
	},
	semiBold: {
		fontFamily: 'PoppinsSemiBold',
		fontWeight: 'normal',
	},
	semiBoldItalic: {
		fontFamily: 'PoppinsSemiBoldItalic',
		fontWeight: 'normal',
	},
	bold: {
		fontFamily: 'PoppinsBold',
		fontWeight: 'normal',
	},
	boldItalic: {
		fontFamily: 'PoppinsBoldItalic',
		fontWeight: 'normal',
	},
	label: {
		fontFamily: 'PoppinsRegular',
		fontWeight: 'normal',
		textTransform: 'uppercase',
	},
};

type FontStyleProps = {
	fontFamily: string;
	fontWeight: NonNullable<TextStyle['fontWeight']>;
	textTransform?: NonNullable<TextStyle['textTransform']>;
	textDecorationLine?: NonNullable<TextStyle['textDecorationLine']>;
};

type FontStyles = {
	regular: FontStyleProps;
	italic: FontStyleProps;
	medium: FontStyleProps;
	mediumItalic: FontStyleProps;
	semiBold: FontStyleProps;
	semiBoldItalic: FontStyleProps;
	bold: FontStyleProps;
	boldItalic: FontStyleProps;
	label: FontStyleProps;
};

// FONT SIZES - tokens for properties like fontSize, lineHeight, and letterSpacing
export const FontSizes = {
	display: {
		xxl: {
			fontSize: 28,
			lineHeight: 32,
			letterSpacing: -1,
		},
		xl: {
			fontSize: 24,
			lineHeight: 28,
			letterSpacing: -1,
		},
		lg: {
			fontSize: 20,
			lineHeight: 24,
			letterSpacing: -1,
		},
		md: {
			fontSize: 18,
			lineHeight: 20,
			letterSpacing: -1,
		},
		sm: {
			fontSize: 16,
			lineHeight: 18,
			letterSpacing: -1,
		},
		xs: {
			fontSize: 14,
			lineHeight: 16,
			letterSpacing: -1,
		},
	},
	body: {
		//sizes for text, actions (links, buttons), and badges
		lg: {
			fontSize: 18,
			lineHeight: 28,
		},
		md: {
			fontSize: 16,
			lineHeight: 24,
		},
		sm: {
			fontSize: 14,
			lineHeight: 20,
		},
		xs: {
			fontSize: 12,
			lineHeight: 18,
		},
	},
	utility: {
		//sizes for chips and labels
		lg: {
			fontSize: 14,
			lineHeight: 20,
		},
		sm: {
			fontSize: 12,
			lineHeight: 24,
		},
		xs: {
			fontSize: 10,
			lineHeight: 20,
		},
	},
};
