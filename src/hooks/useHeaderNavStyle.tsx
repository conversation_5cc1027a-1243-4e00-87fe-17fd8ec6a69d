import { useLayoutEffect } from 'react';
import { TouchableOpacity, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { JellyIcon } from '@/components/ui/JellyIcon';
import { useTheme } from '@/theme';

interface UseHeaderNavStyleOptions {
	title: string;
	/**
	 * Controls React Navigation back button visibility
	 * - Main tabs (Profile, Calendar, Groups, More): false (navigate via tab bar)
	 * - Sub-pages (Events, Settings items, etc.): true (navigate back to parent)
	 * @default true
	 */
	showBackButton?: boolean;
	/**
	 * Controls header title alignment
	 * - Main tabs: 'left' (clean, section-style headers)
	 * - Sub-pages: 'center' (standard drill-down pages)
	 * @default 'center'
	 */
	headerTitleAlign?: 'left' | 'center';
}

/**
 * Hook to configure React Navigation header styling consistently across the app
 *
 * @example Main tab usage (no back button, left-aligned):
 * ```typescript
 * useHeaderNavStyle({
 *   title: 'Profile',
 *   showBackButton: false,
 *   headerTitleAlign: 'left'
 * });
 * ```
 *
 * @example Sub-page usage (with back button, centered):
 * ```typescript
 * useHeaderNavStyle({ title: 'Events' }); // Uses defaults
 * ```
 */
export function useHeaderNavStyle({
	title,
	showBackButton = true,
	headerTitleAlign = 'center',
}: UseHeaderNavStyleOptions) {
	const navigation = useNavigation();
	const theme = useTheme();

	useLayoutEffect(() => {
		navigation.setOptions({
			title,
			headerShown: true,
			headerTitleAlign: headerTitleAlign,
			headerBackVisible: false, // Hide default back button
			headerStyle: {
				backgroundColor: theme.colors.background,
				borderBottomWidth: 1,
				borderBottomColor: theme.colors.border,
			},
			headerTintColor: theme.colors.text,
			headerTitleStyle: {
				...theme.fonts.bold,
			},
			headerLeft: showBackButton
				? () => (
						<TouchableOpacity
							onPress={() => navigation.goBack()}
							style={{
								paddingLeft: Platform.select({ ios: 16, android: 16 }),
								paddingRight: 8,
								paddingVertical: Platform.select({ ios: 8, android: 12 }),
							}}
						>
							<JellyIcon name='angleLeft' color={theme.colors.text} size={20} />
						</TouchableOpacity>
					)
				: undefined,
		});
	}, [navigation, title, theme, showBackButton, headerTitleAlign]);
}
