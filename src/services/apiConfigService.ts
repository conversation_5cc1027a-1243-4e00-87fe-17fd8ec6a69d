/**
 * API Configuration Service
 *
 * Handles all business logic for API key and URL management,
 * including secure storage, validation, and testing.
 */

import { secureStorageRepository } from '../repositories/secureStorageRepository';
import { Platform } from 'react-native';

// __DEV__ is a global variable in React Native
declare const __DEV__: boolean;

// Storage keys for API configuration
export const API_CONFIG_KEYS = {
	API_KEY: 'test_api_key',
	API_BASE_URL: 'api_base_url',
} as const;

// Types
export interface ApiConfig {
	apiKey: string | null;
	apiBaseUrl: string | null;
}

export interface TestResults {
	save: boolean | null;
	retrieve: boolean | null;
	delete: boolean | null;
	exists: boolean | null;
	saveUrl: boolean | null;
	retrieveUrl: boolean | null;
	apiTest: boolean | null;
}

export interface ApiTestResponse {
	success: boolean;
	status?: number;
	data?: any;
	error?: string;
}

export interface DevConfig {
	DEV_UNA_API_KEY?: string | null;
	DEV_UNA_API_BASE_URL?: string | null;
}

/**
 * API Configuration Service
 */
class ApiConfigService {
	/**
	 * Load stored API configuration
	 */
	async loadApiConfig(): Promise<ApiConfig> {
		try {
			const [apiKey, apiBaseUrl] = await Promise.all([
				secureStorageRepository.getItem(API_CONFIG_KEYS.API_KEY),
				secureStorageRepository.getItem(API_CONFIG_KEYS.API_BASE_URL),
			]);

			return {
				apiKey,
				apiBaseUrl,
			};
		} catch (error) {
			console.error('Failed to load API configuration:', error);
			return {
				apiKey: null,
				apiBaseUrl: null,
			};
		}
	}

	/**
	 * Save API key securely
	 */
	async saveApiKey(apiKey: string): Promise<void> {
		if (!apiKey.trim()) {
			throw new Error('API key cannot be empty');
		}

		try {
			await secureStorageRepository.setItem(API_CONFIG_KEYS.API_KEY, apiKey.trim());
		} catch (error) {
			console.error('Failed to save API key:', error);
			throw new Error(`Failed to save API key: ${error}`);
		}
	}

	/**
	 * Save API base URL securely
	 */
	async saveApiBaseUrl(apiBaseUrl: string): Promise<void> {
		if (!apiBaseUrl.trim()) {
			throw new Error('API base URL cannot be empty');
		}

		// Validate URL format
		try {
			new URL(apiBaseUrl.trim());
		} catch {
			throw new Error('Please enter a valid URL (e.g., https://api.example.com)');
		}

		try {
			await secureStorageRepository.setItem(API_CONFIG_KEYS.API_BASE_URL, apiBaseUrl.trim());
		} catch (error) {
			console.error('Failed to save API base URL:', error);
			throw new Error(`Failed to save API base URL: ${error}`);
		}
	}

	/**
	 * Delete all API configuration
	 */
	async deleteApiConfig(): Promise<void> {
		try {
			await Promise.all([
				secureStorageRepository.removeItem(API_CONFIG_KEYS.API_KEY),
				secureStorageRepository.removeItem(API_CONFIG_KEYS.API_BASE_URL),
			]);
		} catch (error) {
			console.error('Failed to delete API configuration:', error);
			throw new Error(`Failed to delete API configuration: ${error}`);
		}
	}

	/**
	 * Load development configuration (only in development mode)
	 */
	async importDevConfig(
		forceReload = false,
	): Promise<{ success: boolean; message: string; config?: DevConfig }> {
		if (!__DEV__) {
			return {
				success: false,
				message: 'Development configuration is only available in development mode',
			};
		}

		try {
			console.log('🔧 Loading dev.config.json...');

			// Try multiple approaches to load dev.config.json
			let devConfig: DevConfig & { _isFallback?: boolean; _message?: string };

			try {
				// First try the Metro resolver alias approach
				console.log('🔧 Attempting to load dev config via @dev-config alias...');
				// eslint-disable-next-line @typescript-eslint/no-require-imports
				devConfig = require('@dev-config');
				console.log('✅ Dev config loaded via alias:', {
					hasApiKey: !!devConfig.DEV_UNA_API_KEY,
					hasApiUrl: !!devConfig.DEV_UNA_API_BASE_URL,
					isFallback: !!devConfig._isFallback,
				});
			} catch (aliasError: any) {
				console.log('⚠️ Alias approach failed, trying direct path...', aliasError.message);

				try {
					// Fallback to direct require - this should work on iOS
					devConfig = require('../../dev.config.json');
					console.log('✅ Dev config loaded via direct path:', {
						hasApiKey: !!devConfig.DEV_UNA_API_KEY,
						hasApiUrl: !!devConfig.DEV_UNA_API_BASE_URL,
					});
				} catch (directError: any) {
					console.log(
						'ℹ️ Direct path failed, dev.config.json not found:',
						directError.message,
					);
					return {
						success: false,
						message:
							'dev.config.json not found. Please create one with DEV_UNA_API_KEY and DEV_UNA_API_BASE_URL, or manually enter your API configuration.',
						config: {
							DEV_UNA_API_KEY: null,
							DEV_UNA_API_BASE_URL: null,
						},
					};
				}
			}

			// Check if this is the fallback configuration
			if (devConfig._isFallback) {
				console.log('ℹ️ Using fallback dev configuration');
				return {
					success: false,
					message:
						devConfig._message ||
						'dev.config.json not found. Please create one with DEV_UNA_API_KEY and DEV_UNA_API_BASE_URL, or manually enter your API configuration.',
					config: {
						DEV_UNA_API_KEY: null,
						DEV_UNA_API_BASE_URL: null,
					},
				};
			}

			// Check if the loaded config has the required fields
			if (!devConfig || !devConfig.DEV_UNA_API_KEY || !devConfig.DEV_UNA_API_BASE_URL) {
				return {
					success: false,
					message:
						'dev.config.json found but missing required fields (DEV_UNA_API_KEY, DEV_UNA_API_BASE_URL)',
					config: {
						DEV_UNA_API_KEY: devConfig?.DEV_UNA_API_KEY || null,
						DEV_UNA_API_BASE_URL: devConfig?.DEV_UNA_API_BASE_URL || null,
					},
				};
			}

			// Check if we already have stored values
			const existingConfig = await this.loadApiConfig();
			const hasExistingConfig = existingConfig.apiKey && existingConfig.apiBaseUrl;

			// Auto-load if we don't have existing values, or if force reload is requested
			if (!hasExistingConfig || forceReload) {
				await Promise.all([
					this.saveApiKey(devConfig.DEV_UNA_API_KEY),
					this.saveApiBaseUrl(devConfig.DEV_UNA_API_BASE_URL),
				]);

				const action = forceReload ? 'reloaded' : 'loaded';
				console.log('✅ Dev configuration loaded successfully');

				return {
					success: true,
					message: `API key and base URL ${action} from dev.config.json`,
					config: devConfig,
				};
			} else {
				return {
					success: true,
					message: 'Existing configuration found, skipping dev config auto-load',
					config: devConfig,
				};
			}
		} catch (error: any) {
			console.log('ℹ️ Error processing dev config:', error.message);
			return {
				success: false,
				message: 'Error processing development configuration. Check console for details.',
				config: {
					DEV_UNA_API_KEY: null,
					DEV_UNA_API_BASE_URL: null,
				},
			};
		}
	}
	/**
	 * Normalize a raw base URL for the current platform/environment.
	 * - In development on Android emulator, map localhost to ********
	 * - Leave production and non-loopback hosts untouched
	 */
	normalizeBaseUrl(raw: string): string {
		if (!raw) return raw;

		try {
			const url = new URL(raw);
			const host = url.hostname;

			const isLoopback = host === 'localhost' || host === '127.0.0.1' || host === '::1';

			if (__DEV__ && Platform.OS === 'android' && isLoopback) {
				url.hostname = '********';
			}

			// Trim trailing slashes for consistency
			return url.toString().replace(/\/+$/, '');
		} catch {
			// If not a valid absolute URL, return as-is
			return raw;
		}
	}
}

// Export singleton instance
export const apiConfigService = new ApiConfigService();
