import { ApiFactory } from '@/services/apiFactory';
import type { ApiResponse } from '@/api/api';
import { Routes } from '@/api/routes';
import { handleAuthError, logError } from '@/utils/errorHandler';

/**
 * User profile structure returned from API
 */
export interface UserProfile {
	id: string;
	email?: string;
	name?: string;
	username?: string;
	firstName?: string;
	lastName?: string;
	roles?: string[];
	// Additional fields that might be returned by the API
	[key: string]: any;
}

export interface UserService {
	getProfile(): Promise<UserProfile>;
}

class DefaultUserService implements UserService {
	private api = ApiFactory.getApiClient();

	async getProfile(): Promise<UserProfile> {
		try {
			if (__DEV__)
				console.log(
					'[UserService] Fetching user profile via ApiClient using Routes.user.me',
				);

			const { data: userData }: ApiResponse<UserProfile> = await this.api.request(
				Routes.user.me(),
			);

			if (__DEV__) {
				console.log('[UserService] User profile fetch response:', userData);
			}

			if (!userData) {
				throw new Error('Failed to fetch user profile');
			}

			return userData;
		} catch (error: unknown) {
			logError('UserService.getProfile', error);

			// Use centralized error handler for auth-related errors
			const errorMessage = await handleAuthError(error);
			throw new Error(errorMessage);
		}
	}
}

export const createUserService = async (): Promise<UserService> => {
	return new DefaultUserService();
};
