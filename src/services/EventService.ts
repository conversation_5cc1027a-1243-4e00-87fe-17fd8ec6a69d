import { ApiFactory } from '@/services/apiFactory';
import type { ApiResponse, ApiClient } from '@/api/api';
import { Routes } from '@/api/routes';
import { UnaApiResponse, UnaEvent } from '@/features/events/types/UnaApiTypes';
import { EventType } from '@/features/events/types/shared';
import {
	validateEventId,
	validateRSVPStatus,
	validateEventType,
} from '@/features/events/validation/schemas';
import {
	EventNotFoundError,
	EventUpdateError,
	EventFetchError,
} from '@/features/events/errors/EventErrors';
import { logger } from '@/utils/logger';

const serviceLogger = logger.child('EventService');

/**
 * Events API Response types
 */
export interface EventsApiResponse {
	events: UnaApiResponse;
	success: boolean;
	type: EventType;
	message?: string;
}

export interface EventDetailApiResponse {
	raw_event: UnaEvent;
	success: boolean;
	message?: string;
}

/**
 * Event Service - handles all event-related API calls
 * Uses dependency injection for better testability
 * Implements request deduplication and caching
 */
export class EventService {
	// Map to track in-flight requests for deduplication
	private pendingRequests = new Map<string, Promise<any>>();

	// Simple cache with TTL
	private cache = new Map<string, { data: any; timestamp: number }>();
	private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

	/**
	 * @param api - API client instance (injectable for testing)
	 */
	constructor(private api: ApiClient = ApiFactory.getApiClient()) {}

	/**
	 * Clear cache for a specific key or all cache
	 */
	clearCache(key?: string): void {
		if (key) {
			this.cache.delete(key);
		} else {
			this.cache.clear();
		}
	}

	/**
	 * Get cached data if still valid
	 */
	private getCached<T>(key: string): T | null {
		const cached = this.cache.get(key);
		if (!cached) return null;

		const now = Date.now();
		if (now - cached.timestamp > this.CACHE_TTL) {
			this.cache.delete(key);
			return null;
		}

		return cached.data as T;
	}

	/**
	 * Set cache data
	 */
	private setCache(key: string, data: any): void {
		this.cache.set(key, { data, timestamp: Date.now() });
	}

	/**
	 * Fetch events by type (current, upcoming, previous, discover)
	 * Implements deduplication and caching
	 * @throws ZodError if type is invalid
	 * @throws EventFetchError if fetch fails
	 */
	async fetchEvents(
		type: 'current' | 'upcoming' | 'previous' | 'discover',
	): Promise<ApiResponse<EventsApiResponse>> {
		// Validate input
		const validatedType = validateEventType(type);
		const cacheKey = `events:${validatedType}`;

		// Check cache first
		const cached = this.getCached<ApiResponse<EventsApiResponse>>(cacheKey);
		if (cached) {
			serviceLogger.debug(`Cache hit for ${validatedType} events`);
			return cached;
		}

		// Check if request is already in flight
		if (this.pendingRequests.has(cacheKey)) {
			serviceLogger.debug(`Deduplicating request for ${validatedType} events`);
			return this.pendingRequests.get(cacheKey)!;
		}

		// Make new request
		const request = this._fetchEventsInternal(validatedType, cacheKey);
		this.pendingRequests.set(cacheKey, request);

		try {
			const result = await request;
			return result;
		} finally {
			// Clean up pending request after a short delay
			setTimeout(() => this.pendingRequests.delete(cacheKey), 1000);
		}
	}

	/**
	 * Internal fetch implementation
	 */
	private async _fetchEventsInternal(
		type: 'current' | 'upcoming' | 'previous' | 'discover',
		cacheKey: string,
	): Promise<ApiResponse<EventsApiResponse>> {
		try {
			const response = await this.api.request<EventsApiResponse>(Routes.events.fetch(type));

			// Cache successful responses (assuming successful if data exists)
			if (response.data) {
				this.setCache(cacheKey, response);
			}

			return response;
		} catch (error) {
			serviceLogger.error(`Failed to fetch ${type} events`, { error });
			throw new EventFetchError(type, error instanceof Error ? error.message : undefined);
		}
	}

	/**
	 * Fetch event details by ID
	 * No caching for details as they may change frequently
	 * @throws ZodError if eventId is invalid
	 * @throws EventNotFoundError if event not found
	 */
	async fetchEventDetails(eventId: string): Promise<ApiResponse<EventDetailApiResponse>> {
		// Validate input
		const validatedId = validateEventId(eventId);
		const cacheKey = `event:${validatedId}`;

		// Check if request is already in flight (deduplication only)
		if (this.pendingRequests.has(cacheKey)) {
			serviceLogger.debug(`Deduplicating request for event ${validatedId}`);
			return this.pendingRequests.get(cacheKey)!;
		}

		const request = this._fetchEventDetailsInternal(validatedId);
		this.pendingRequests.set(cacheKey, request);

		try {
			return await request;
		} finally {
			setTimeout(() => this.pendingRequests.delete(cacheKey), 1000);
		}
	}

	/**
	 * Internal event details fetch implementation
	 */
	private async _fetchEventDetailsInternal(
		eventId: string,
	): Promise<ApiResponse<EventDetailApiResponse>> {
		try {
			const response = await this.api.request<EventDetailApiResponse>(
				Routes.events.getDetails(eventId),
			);

			// Check if data exists
			if (!response.data || !response.data.raw_event) {
				throw new EventNotFoundError(eventId);
			}

			return response;
		} catch (error) {
			if (error instanceof EventNotFoundError) {
				throw error;
			}
			serviceLogger.error(`Failed to fetch event ${eventId}`, { error });
			throw new EventNotFoundError(eventId);
		}
	}

	/**
	 * Update event status (RSVP)
	 * Invalidates cache on success
	 * @throws ZodError if eventId or status is invalid
	 * @throws EventUpdateError if update fails
	 */
	async updateEventStatus(
		eventId: string,
		status: 'going' | 'interested' | 'not_going',
	): Promise<ApiResponse<any>> {
		// Validate inputs
		const validatedId = validateEventId(eventId);
		const validatedStatus = validateRSVPStatus(status);

		try {
			const response = await this.api.request(
				Routes.events.updateStatus(validatedId, validatedStatus),
			);

			// Invalidate all event caches on successful update
			if (response.data) {
				serviceLogger.debug(`Clearing cache after updating event ${validatedId}`);
				this.clearCache(); // Clear all caches to ensure fresh data
			}

			return response;
		} catch (error) {
			serviceLogger.error(`Failed to update event ${eventId} status`, { error, status });
			throw new EventUpdateError(eventId, error instanceof Error ? error.message : undefined);
		}
	}
}

/**
 * Export singleton instance
 */
export const eventService = new EventService();
