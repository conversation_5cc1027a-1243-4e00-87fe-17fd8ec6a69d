import { ApiFactory } from '@/services/apiFactory';
import { Post } from '@/features/posts/model/post';
import { Routes } from '@/api/routes';

export class PostService {
	private apiClient = ApiFactory.getApiClient();

	async fetchPosts(): Promise<Post[]> {
		const { data } = await this.apiClient.request(Routes.posts.fetch());

		return data['data'];
	}

	async submitPost(title: string, message: string): Promise<boolean> {
		await this.apiClient.request(Routes.posts.post(title, message));
		return true;
	}
}
