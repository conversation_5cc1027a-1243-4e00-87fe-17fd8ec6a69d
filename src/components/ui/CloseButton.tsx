import React from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { JellyIcon } from '@/components/ui/JellyIcon';
import { useTheme, Tokens } from '@/theme';
import { useRouter } from 'expo-router';
type CustomButtonProps = TouchableOpacityProps;

export default function CloseButton(props: CustomButtonProps) {
	const { onPress, ...rest } = props;
	const theme = useTheme();
	const router = useRouter();

	const onPressHandler = (event: any) => {
		if (onPress) {
			onPress(event);
		} else {
			router.back();
		}
	};

	return (
		<TouchableOpacity onPress={onPressHandler} {...rest}>
			<JellyIcon
				name='xmark'
				variant='fill'
				color={theme.colors.primary}
				size={Tokens.sizes.icon}
			/>
		</TouchableOpacity>
	);
}
