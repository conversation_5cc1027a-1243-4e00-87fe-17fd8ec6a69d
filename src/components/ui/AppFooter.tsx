import React, { useState } from 'react';
import { StyleSheet, TouchableOpacity, View, Modal, Alert } from 'react-native';
import { useTranslation } from 'react-i18next';
import * as Linking from 'expo-linking';
import { useSelector, useDispatch } from 'react-redux';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useTheme } from '@/theme';
import { selectIsAuthenticated, logoutUser } from '@/features/user/userSlice';
import type { AppDispatch } from '@/store';

export default function AppFooter() {
	const { t } = useTranslation();
	const theme = useTheme();
	const dispatch = useDispatch<AppDispatch>();
	const isAuthenticated = useSelector(selectIsAuthenticated);
	const [showCopyrightModal, setShowCopyrightModal] = useState(false);

	const footerItems = [
		{
			key: 'communityRules',
			label: t('footer.communityRules'),
			url: 'https://stride.tiltedwindmillagency.com/about',
		},
		{
			key: 'privacy',
			label: t('footer.privacy'),
			url: 'https://stride.tiltedwindmillagency.com/privacy',
		},
		{
			key: 'termsOfService',
			label: t('footer.termsOfService'),
			url: 'https://stride.tiltedwindmillagency.com/terms',
		},
	];

	const handleItemPress = async (item: any) => {
		try {
			const canOpen = await Linking.canOpenURL(item.url);
			if (canOpen) {
				await Linking.openURL(item.url);
			} else {
				Alert.alert(t('common.error'), t('footer.cannotOpenUrl'));
			}
		} catch (error) {
			console.error('Error opening URL:', error);
			Alert.alert(t('common.error'), t('footer.cannotOpenUrl'));
		}
	};

	const handleSignOut = () => {
		Alert.alert(t('auth.signOut'), t('auth.signOutConfirmation'), [
			{
				text: t('common.cancel'),
				style: 'cancel',
			},
			{
				text: t('auth.signOut'),
				style: 'destructive',
				onPress: () => {
					dispatch(logoutUser());
				},
			},
		]);
	};

	const handleCopyrightPress = () => {
		setShowCopyrightModal(true);
	};

	const currentYear = new Date().getFullYear();

	return (
		<>
			<ThemedView style={styles.container}>
				{/* Divider */}
				<View style={styles.divider}>
					<View style={[styles.dividerLine, { backgroundColor: theme.colors.border }]} />
				</View>

				{/* Footer Items */}
				{footerItems.map((item) => (
					<TouchableOpacity
						key={item.key}
						style={styles.footerItem}
						onPress={() => handleItemPress(item)}
						activeOpacity={0.7}
					>
						<ThemedText style={styles.footerText}>{item.label}</ThemedText>
					</TouchableOpacity>
				))}

				{/* Sign Out (only show if authenticated) */}
				{isAuthenticated && (
					<TouchableOpacity
						style={styles.footerItem}
						onPress={handleSignOut}
						activeOpacity={0.7}
					>
						<ThemedText style={styles.footerText}>{t('auth.signOut')}</ThemedText>
					</TouchableOpacity>
				)}

				{/* Copyright */}
				<TouchableOpacity
					style={styles.footerItem}
					onPress={handleCopyrightPress}
					activeOpacity={0.7}
				>
					<ThemedText style={styles.footerText}>
						{t('footer.copyright', { year: currentYear })}
					</ThemedText>
				</TouchableOpacity>
			</ThemedView>

			{/* Copyright Modal */}
			<Modal
				visible={showCopyrightModal}
				transparent={true}
				animationType='fade'
				onRequestClose={() => setShowCopyrightModal(false)}
			>
				<View style={styles.modalOverlay}>
					<ThemedView
						style={[styles.modalContent, { backgroundColor: theme.colors.background }]}
					>
						<ThemedText style={styles.modalText}>
							© {currentYear} Stride Inc.
						</ThemedText>
						<TouchableOpacity
							style={[styles.modalButton, { backgroundColor: theme.colors.tint }]}
							onPress={() => setShowCopyrightModal(false)}
						>
							<ThemedText
								style={[styles.modalButtonText, { color: theme.colors.background }]}
							>
								{t('common.ok')}
							</ThemedText>
						</TouchableOpacity>
					</ThemedView>
				</View>
			</Modal>
		</>
	);
}

const styles = StyleSheet.create({
	container: {
		paddingBottom: 24,
		gap: 16,
	},
	divider: {
		paddingVertical: 8,
		paddingHorizontal: 16,
	},
	dividerLine: {
		height: 1,
		opacity: 0.93,
	},
	footerItem: {
		paddingVertical: 0,
		paddingHorizontal: 16,
		alignItems: 'flex-start',
		flexDirection: 'row',
	},
	footerText: {
		fontSize: 12,
		letterSpacing: -0.1,
		lineHeight: 18,
		fontWeight: '500',
		textAlign: 'left',
	},
	modalOverlay: {
		flex: 1,
		backgroundColor: 'rgba(0, 0, 0, 0.5)',
		justifyContent: 'center',
		alignItems: 'center',
	},
	modalContent: {
		margin: 20,
		borderRadius: 12,
		padding: 24,
		alignItems: 'center',
		shadowColor: '#000',
		shadowOffset: {
			width: 0,
			height: 2,
		},
		shadowOpacity: 0.25,
		shadowRadius: 4,
		elevation: 5,
		minWidth: 200,
	},
	modalText: {
		fontSize: 16,
		fontWeight: '600',
		marginBottom: 20,
		textAlign: 'center',
	},
	modalButton: {
		borderRadius: 8,
		paddingVertical: 12,
		paddingHorizontal: 24,
		minWidth: 80,
	},
	modalButtonText: {
		fontSize: 16,
		fontWeight: '600',
		textAlign: 'center',
	},
});
