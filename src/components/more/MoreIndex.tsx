import React from 'react';
import { ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { JellyIcon } from '@/components/ui/JellyIcon';
import { useTheme, Tokens } from '@/theme';
import { navConfig, resolveUrl } from '@/config/navigation';

export default function MoreIndex() {
	const theme = useTheme();
	const router = useRouter();
	const { t } = useTranslation();

	// Find the More tab configuration and its subItems
	const moreTab = navConfig.find((item) => item.name === 'more');
	const subItems = moreTab?.subItems || [];

	const handleItemPress = (item: any) => {
		if (item.pageUrl) {
			// Resolve relative URLs to full URLs
			const fullUrl = resolveUrl(moreTab?.pageUrl || '/(tabs)/more', item.pageUrl);
			router.push(fullUrl as any);
		} else {
			// For items without pageUrl, navigate to dynamic route
			router.push(`/more/${item.name}` as any);
		}
	};

	return (
		<ThemedView style={styles.container}>
			<ScrollView style={styles.scrollView}>
				{subItems.map((item) => (
					<TouchableOpacity
						key={item.name}
						style={[
							styles.item,
							{
								backgroundColor: theme.colors.background,
							},
						]}
						onPress={() => handleItemPress(item)}
						activeOpacity={0.7}
					>
						<ThemedView style={styles.itemContent}>
							<JellyIcon
								name={item.icon as any}
								variant={item.iconVariant as any}
								color={theme.colors.text}
								size={24}
								style={styles.icon}
							/>
							<ThemedText style={styles.itemText}>
								{t(item.translationKey)}
							</ThemedText>
							<JellyIcon name='angleRight' color={theme.colors.text} size={16} />
						</ThemedView>
					</TouchableOpacity>
				))}
			</ScrollView>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
	scrollView: {
		flex: 1,
		paddingTop: 32, // 32px from header divider
	},
	item: {
		marginHorizontal: Tokens.spacing.md,
	},
	itemContent: {
		flexDirection: 'row',
		alignItems: 'center',
		padding: Tokens.spacing.md,
		minHeight: 56,
	},
	icon: {
		marginRight: Tokens.spacing.md,
		width: 24,
		textAlign: 'center',
	},
	itemText: {
		flex: 1,
		fontSize: 16,
		letterSpacing: -0.2,
		lineHeight: 24,
		fontWeight: '500',
	},
});
