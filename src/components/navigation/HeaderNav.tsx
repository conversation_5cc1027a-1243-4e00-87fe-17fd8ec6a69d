import React from 'react';
import { View, TouchableOpacity, StyleSheet, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';

import { JellyIcon } from '@/components/ui/JellyIcon';
import { useTheme } from '@/theme';
import { navHeaderConfig } from '@/config/navigation';

export default function HeaderNav() {
	const theme = useTheme();
	const router = useRouter();

	const handleItemPress = (item: any) => {
		if (item.name !== 'index' && item.pageUrl) {
			// Navigate to the item's page - router will handle presentation type
			router.push(item.pageUrl as any);
		}
	};

	// Get header items (excluding the index/home item)
	const headerItems = navHeaderConfig.filter((item) => item.name !== 'index');

	return (
		<SafeAreaView
			style={[styles.container, { backgroundColor: theme.colors.background }]}
			edges={[]} // No edges - HeaderNav is used within existing header containers
		>
			{/* Left Side - Empty Space for consistent layout */}
			<View style={styles.leftSide} />

			{/* Right Side - Navigation Items */}
			<View style={styles.navItems}>
				{headerItems.map((item) => (
					<TouchableOpacity
						key={item.name}
						style={styles.iconButton}
						onPress={() => handleItemPress(item)}
						activeOpacity={0.7}
					>
						<JellyIcon
							name={item.icon as any}
							variant={item.iconVariant as any}
							color={theme.colors.text}
							size={24}
						/>
					</TouchableOpacity>
				))}
			</View>
		</SafeAreaView>
	);
}

const styles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'space-between',
		paddingHorizontal: 16,
		paddingTop: Platform.select({
			ios: 44, // Reduced padding on iOS since SafeAreaView handles status bar
			android: 32, // More padding on Android for consistent spacing
		}),
		paddingBottom: 8,
		minHeight: 44, // Standard header height
		backgroundColor: 'transparent',
	},
	leftSide: {
		width: 44, // Fixed width to maintain consistent spacing
		height: 44,
		justifyContent: 'center',
		alignItems: 'flex-start',
	},
	navItems: {
		flexDirection: 'row',
		alignItems: 'center',
		gap: 12,
	},
	iconButton: {
		padding: 8,
		borderRadius: 8,
		minWidth: 44,
		minHeight: 44,
		justifyContent: 'center',
		alignItems: 'center',
	},
});
