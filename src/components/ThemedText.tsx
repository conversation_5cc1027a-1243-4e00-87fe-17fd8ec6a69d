import { StyleSheet, Text, type TextProps } from 'react-native';
import { useTheme, FontStyles, FontSizes, Colors } from '@/theme';
export type ThemedTextProps = TextProps & {
	lightColor?: string;
	darkColor?: string;
	type?:
		| 'default'
		| 'title'
		| 'defaultSemiBold'
		| 'subtitle'
		| 'link'
		| 'buttonCaption'
		| 'error';
};

export function ThemedText({
	style,
	lightColor,
	darkColor,
	type = 'default',
	...rest
}: ThemedTextProps) {
	const { colors } = useTheme();
	const typeColors: Record<NonNullable<ThemedTextProps['type']>, string> = {
		default: colors.text,
		title: colors.text,
		defaultSemiBold: colors.text,
		subtitle: colors.text,
		buttonCaption: colors.background,
		link: colors.tint ?? colors.primary, // fallback, jeśli nie masz tint
		error: 'red',
	};

	return (
		<Text
			style={[
				{ color: typeColors[type] },
				type === 'default' ? styles.default : undefined,
				type === 'title' ? styles.title : undefined,
				type === 'defaultSemiBold' ? styles.defaultSemiBold : undefined,
				type === 'subtitle' ? styles.subtitle : undefined,
				type === 'link' ? styles.link : undefined,
				type === 'buttonCaption' ? styles.buttonCaption : undefined,
				type === 'error' ? styles.error : undefined,
				style,
			]}
			{...rest}
		/>
	);
}

const styles = StyleSheet.create({
	default: {
		...FontStyles.regular,
		...FontSizes.body.md,
	},
	defaultSemiBold: {
		...FontStyles.semiBold,
		...FontSizes.body.md,
	},
	title: {
		...FontStyles.bold,
		...FontSizes.display.xxl,
	},
	subtitle: {
		...FontStyles.bold,
		...FontSizes.display.lg,
	},
	buttonCaption: {
		...FontStyles.regular,
		...FontSizes.body.md,
		textAlign: 'center',
	},
	link: {
		...FontStyles.regular,
		color: Colors.text.brand,
	},
	error: {
		...FontStyles.regular,
		color: Colors.text.errorPrimary,
	},
});
