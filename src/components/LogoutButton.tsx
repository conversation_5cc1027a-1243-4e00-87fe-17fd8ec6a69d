/** TODO: Drop it later
 * Logout Button Component
 *
 * Reusable logout button that integrates with Redux and theme system
 */

import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { useDispatch, useSelector } from 'react-redux';
import { logoutUser, selectIsAuthenticated } from '@/features/user/userSlice';
import { useTheme, Tokens } from '@/theme';
import type { AppDispatch } from '@/store';
import { showConfirm } from '@/utils/alert';
import { JellyIcon } from '@/components/ui/JellyIcon';
interface LogoutButtonProps {
	variant?: 'header' | 'full';
	onLogout?: () => void;
}

export default function LogoutButton({ variant = 'header', onLogout }: LogoutButtonProps) {
	const dispatch = useDispatch<AppDispatch>();
	const isAuthenticated = useSelector(selectIsAuthenticated);
	const theme = useTheme();

	const handleLogout = () => {
		showConfirm('Logout', 'Are you sure you want to logout?', () => {
			console.log('🚪 User logged out');
			dispatch(logoutUser());
			onLogout?.();
			// Let protected routes handle redirection automatically
			// This relies on the fallback in _layout.tsx
		});
	};

	// Don't render if user is not authenticated
	if (!isAuthenticated) {
		return null;
	}

	const styles = createStyles(theme, variant);

	return (
		<TouchableOpacity
			style={styles.button}
			onPress={handleLogout}
			accessibilityLabel='Logout'
			accessibilityRole='button'
		>
			{variant === 'header' ? (
				<JellyIcon
					name='lock'
					variant='fill'
					color={theme.colors.tint}
					size={Tokens.sizes.icon}
				/>
			) : (
				<Text style={styles.text}>Logout</Text>
			)}
		</TouchableOpacity>
	);
}

const createStyles = (theme: ReturnType<typeof useTheme>, variant: 'header' | 'full') => {
	const isHeader = variant === 'header';

	return StyleSheet.create({
		button: {
			backgroundColor: isHeader ? 'transparent' : theme.colors.primary,
			paddingHorizontal: isHeader ? 12 : 24,
			paddingVertical: isHeader ? 6 : 12,
			borderRadius: isHeader ? 6 : 8,
			borderWidth: isHeader ? 0 : 0,
			borderColor: isHeader ? theme.colors.border : 'transparent',
			alignItems: 'center',
			justifyContent: 'center',
			minHeight: isHeader ? 32 : 44,
			// padding: isHeader ? 10 : 10,
		},
		text: {
			color: isHeader ? theme.colors.text : theme.colors.background,
			fontSize: isHeader ? 14 : 16,
			...theme.fonts.medium,
		},
	});
};
