import React from 'react';
import { StyleSheet } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';

export default function ResourceLibrary() {
	const { t } = useTranslation();

	useHeaderNavStyle({ title: t('features.resourceLibrary.title') });

	return (
		<ThemedView style={styles.container}>
			<ThemedText type='title'>{t('features.resourceLibrary.title')}</ThemedText>
			<ThemedText type='default' style={styles.description}>
				{t('features.resourceLibrary.description')}
			</ThemedText>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
	description: {
		textAlign: 'center',
		marginTop: 16,
		opacity: 0.7,
	},
});
