import React from 'react';
import { ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { JellyIcon } from '@/components/ui/JellyIcon';
import { useTheme } from '@/theme';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';
import { navConfig } from '@/config/navigation';
import AppFooter from '@/components/ui/AppFooter';

export default function Settings() {
	const { t } = useTranslation();
	const theme = useTheme();
	const router = useRouter();

	useHeaderNavStyle({
		title: t('features.settings.title'),
		showBackButton: false,
		headerTitleAlign: 'left',
	});

	// Find the Settings navigation item and its subItems
	const moreTab = navConfig.find((item) => item.name === 'more');
	const settingsItem = moreTab?.subItems?.find((item) => item.name === 'settings');
	const settingsSubItems = settingsItem?.subItems || [];

	const handleItemPress = (item: any) => {
		// Navigate to the settings sub-item using dynamic routing
		router.push(`/more/settings/${item.name}` as any);
	};

	return (
		<ThemedView style={styles.container}>
			<ScrollView style={styles.scrollView}>
				{settingsSubItems.map((item) => (
					<TouchableOpacity
						key={item.name}
						style={[
							styles.item,
							{
								backgroundColor: theme.colors.background,
							},
						]}
						onPress={() => handleItemPress(item)}
						activeOpacity={0.7}
					>
						<ThemedView style={styles.itemContent}>
							<JellyIcon
								name={item.icon as any}
								variant={item.iconVariant as any}
								color={theme.colors.text}
								size={24}
								style={styles.icon}
							/>
							<ThemedText style={styles.itemText}>
								{t(item.translationKey)}
							</ThemedText>
							<JellyIcon name='angleRight' color={theme.colors.text} size={16} />
						</ThemedView>
					</TouchableOpacity>
				))}

				{/* Footer */}
				<AppFooter />
			</ScrollView>
		</ThemedView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
	},
	scrollView: {
		flex: 1,
		paddingTop: 32, // 32px from header divider
	},
	item: {
		marginHorizontal: 16,
	},
	itemContent: {
		flexDirection: 'row',
		alignItems: 'center',
		padding: 16,
		minHeight: 56,
	},
	icon: {
		marginRight: 16,
		width: 24,
		textAlign: 'center',
	},
	itemText: {
		flex: 1,
		fontSize: 16,
		letterSpacing: -0.2,
		lineHeight: 24,
		fontWeight: '500',
	},
});
