/**
 * User Redux Slice
 *
 * Manages user authentication state and profile data
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { t } from 'i18next';
import { ComposePostState } from './model/state';
import { PostService } from '@/services/PostService';
import type { RootState } from '@/store';

const initialState: ComposePostState = {
	title: '',
	message: '',
	loading: false,
	error: null,
	success: false,
};

export const submitPost = createAsyncThunk<
	boolean,
	{ title: string; message: string },
	{ rejectValue: string }
>('posts/submitPost', async ({ title, message }, { rejectWithValue }) => {
	try {
		const service = new PostService();
		await service.submitPost(title, message);
		return true;
	} catch (error: any) {
		return rejectWithValue(error?.response?.data || t('errors.unknownError'));
	}
});

const postsSlice = createSlice({
	name: 'composePost',
	initialState,
	reducers: {
		setMessage: (state, action: PayloadAction<string>) => {
			state.message = action.payload;
		},
		resetMessage: (state) => {
			state.message = '';
			state.success = false;
			state.title = '';
		},
		setTitle: (state, action: PayloadAction<string>) => {
			state.title = action.payload;
		},
		closeComposer: (state) => {
			state.success = false;
			state.loading = false;
			state.title = '';
			state.message = '';
			state.error = null;
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(submitPost.pending, (state) => {
				state.loading = true;
				state.error = null;
				state.success = false;
			})
			.addCase(submitPost.fulfilled, (state) => {
				state.success = true;
			})
			.addCase(submitPost.rejected, (state, action) => {
				state.loading = false;
				state.error = action.payload as string;
				state.success = false;
			});
	},
});

export default postsSlice.reducer;
export const { setTitle, setMessage, closeComposer } = postsSlice.actions;
export const selectSuccess = (state: RootState) => state.composePost.success;
export const selectLoading = (state: RootState) => state.composePost.loading;
