import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { ThemedText } from '@/components/ThemedText';
import { useSelector, useDispatch } from 'react-redux';
import { PostAuthor } from './PostAuthor';
import type { AppDispatch } from '@/store';
import {
	fetchPosts,
	selectPosts,
	selectIsLoading,
	selectIsInitialized,
	selectError,
} from '@/features/posts/postsSlice';
import { LoadingIndicator } from '@/components/LoadingIndicator';
import { selectIsAuthenticated } from '@/features/user/userSlice';

/**
 * PostsList - Renders posts as a simple list for use inside ScrollView
 * Use this when you need posts inside a parent ScrollView component
 */
export default function PostsList() {
	const posts = useSelector(selectPosts);
	const isLoading = useSelector(selectIsLoading);
	const isInitialized = useSelector(selectIsInitialized);
	const error = useSelector(selectError);
	const isUserAuthenticated = useSelector(selectIsAuthenticated);
	const dispatch = useDispatch<AppDispatch>();

	useEffect(() => {
		if (!isInitialized && !isLoading && isUserAuthenticated) {
			dispatch(fetchPosts());
		}
	}, [isInitialized, isLoading, isUserAuthenticated, dispatch]);

	if (isLoading) {
		return (
			<ThemedView style={styles.container}>
				<LoadingIndicator />
			</ThemedView>
		);
	}

	if (error) {
		return (
			<ThemedView style={styles.container}>
				<ThemedText type='error'>Error: {error}</ThemedText>
			</ThemedView>
		);
	}

	// Helper function to render a single post
	const renderPost = (item: any) => (
		<ThemedView key={String(item.id)} style={styles.post}>
			<PostAuthor author={item.author_data} />
			<ThemedText type='subtitle'>{item.content.title}</ThemedText>
			<ThemedText>{item.content.text}</ThemedText>
			<ThemedText>{new Date(item.date * 1000).toLocaleString()}</ThemedText>
		</ThemedView>
	);

	return <View style={styles.container}>{posts.map(renderPost)}</View>;
}

const styles = StyleSheet.create({
	container: {
		paddingBottom: 80, // Space for floating buttons
	},
	post: {
		padding: 16,
		marginBottom: 12,
		backgroundColor: '#eee',
		borderRadius: 8,
		maxWidth: 500,
		minWidth: '100%',
	},
});
