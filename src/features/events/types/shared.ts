/**
 * Shared type definitions for the Events feature
 *
 * This file centralizes common types used throughout the Events feature
 * to ensure consistency and avoid duplication.
 */

/**
 * Event types for API requests and filtering
 */
export type EventType = 'current' | 'previous' | 'upcoming' | 'discover';

/**
 * Event RSVP status values
 */
export type EventStatus = 'going' | 'interested' | 'not_going';

/**
 * Event section types for organization in UI
 */
export type EventSectionType = 'yourEvents' | 'groupEvents' | 'discoverEvents' | 'pastEvents';

/**
 * Loading states for API requests
 */
export type LoadingState = 'idle' | 'loading' | 'succeeded' | 'failed';

/**
 * Common props for event-related components
 */
export interface EventActionProps {
	eventId: string;
	onPress?: () => void;
}
