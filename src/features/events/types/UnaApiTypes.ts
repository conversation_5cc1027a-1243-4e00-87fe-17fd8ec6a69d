/**
 * Types representing the raw Una API response structure
 */

/**
 * Main Una API response structure
 */
export interface UnaApiResponse {
	data: UnaContainer[] | UnaDataContainer;
	status?: number;
	error?: boolean;
	message?: string;
}

/**
 * Alternative data container structure that might appear directly in response.data
 */
export interface UnaDataContainer {
	type?: string;
	data?: any;
	id?: number | string;
	[key: string]: any;
}

/**
 * Container object in Una API response
 */
export interface UnaContainer {
	id?: number | string;
	type?: string;
	data?: {
		data?: UnaEvent[];
		[key: string]: any;
	};
	[key: string]: any;
}

/**
 * Una Event object structure based on actual API response
 */
export interface UnaEvent {
	// Basic identifiers
	id: string | number;
	module?: string;
	module_title?: string;

	// Dates (may be Unix timestamps)
	added?: number;
	date_start?: number;
	date_end?: number;
	date?: string;
	end_date?: string;

	// Content
	title?: string;
	event_name?: string;
	description?: string;
	location?: string;
	url?: string;
	threshold?: number;

	// Author/organizer info
	author?: number;
	author_data?: {
		id?: number;
		display_name?: string;
		url?: string;
		url_avatar?: string;
		module?: string;
	};

	// Event properties
	virtual?: boolean;
	attendees?: number;
	attendeesList?: UnaAttendee[];
	visibility?: string;

	// RSVP status
	current_user_rsvp?: string;
	rsvp?: string;

	// Images and media
	image?: boolean;
	images?: {
		thumbnail?: string;
	};
	thumbnail?: string;
	cover?: {
		storage?: string;
		[key: string]: any;
	};

	// Metadata
	meta?: {
		object?: string;
		config?: string;
		persistent?: number;
		params?: unknown[];
		items?: unknown[];
	};
	location_data?: unknown[];
}

/**
 * Una Attendee object structure
 */
export interface UnaAttendee {
	id: string;
	name?: string;
	display_name?: string;
	url?: string;
	url_avatar?: string;
}

/**
 * Status mapping types
 */
export type UnaEventStatus = 'going' | 'interested' | 'not_going';
export type UnaEventAction = 'join' | 'interested' | 'leave';

/**
 * Map between frontend status values and Una API action values
 */
export interface StatusMapping {
	frontend: UnaEventStatus;
	una: UnaEventAction;
}
