/**
 * Una Event Adapter
 *
 * Transforms raw Una API responses into the application's EventData model.
 * Handles nested API structures and provides type-safe event transformations.
 *
 * @module adapters/unaEventAdapter
 */

import { UnaEvent, UnaContainer } from '../types/UnaApiTypes';
import { EventData, RawEventData } from '../model/types';
import { transformEventData } from '../model/transformers';
import { logger } from '@/utils/logger';
import config from '@/config';

const adapterLogger = logger.child('UnaEventAdapter');

/**
 * Type guard to check if an object is a browse container
 */
function isBrowseContainer(container: unknown): container is UnaContainer {
	return (
		typeof container === 'object' &&
		container !== null &&
		'type' in container &&
		container.type === 'browse' &&
		'data' in container
	);
}

/**
 * Extracts events from a single Una container
 */
function extractEventsFromContainer(container: UnaContainer): UnaEvent[] {
	if (!container.data?.data || !Array.isArray(container.data.data)) {
		return [];
	}

	const events = container.data.data.filter(
		(event): event is UnaEvent => event !== null && typeof event === 'object' && 'id' in event,
	);

	if (__DEV__ && events.length > 0) {
		adapterLogger.debug(`Extracted ${events.length} events from container`);
	}

	return events;
}

/**
 * Converts a Unix timestamp to ISO string, returns null if invalid
 */
function timestampToISOString(timestamp: number | undefined): string | null {
	if (!timestamp || timestamp === 0) {
		return null;
	}

	try {
		return new Date(timestamp * 1000).toISOString();
	} catch (error) {
		adapterLogger.warn('Invalid timestamp', { timestamp });
		return null;
	}
}

/**
 * Converts a date string to ISO string, returns null if invalid
 */
function dateStringToISOString(dateStr: string | undefined): string | null {
	if (!dateStr) {
		return null;
	}

	try {
		const date = new Date(dateStr);
		return isNaN(date.getTime()) ? null : date.toISOString();
	} catch (error) {
		adapterLogger.warn('Invalid date string', { dateStr });
		return null;
	}
}

/**
 * Extracts start date from Una event using multiple fallback strategies
 * Returns null if no valid date found - caller must handle
 */
function extractStartDate(unaEvent: UnaEvent): string | null {
	return (
		timestampToISOString(unaEvent.date_start) ||
		timestampToISOString(unaEvent.added) ||
		dateStringToISOString(unaEvent.date) ||
		null
	);
}

/**
 * Extracts end date from Una event, returns null if not available
 */
function extractEndDate(unaEvent: UnaEvent): string | null {
	return timestampToISOString(unaEvent.date_end) || dateStringToISOString(unaEvent.end_date);
}

/**
 * Extracts RSVP status with type safety
 */
function extractRSVPStatus(unaEvent: UnaEvent): 'going' | 'interested' | 'not_going' {
	const status = unaEvent.current_user_rsvp || unaEvent.rsvp;

	if (status === 'going' || status === 'interested' || status === 'not_going') {
		return status;
	}

	return 'not_going';
}

/**
 * Transforms Una attendees list to app format
 */
function transformAttendeesList(attendeesList: unknown): { id: string; name: string }[] {
	if (!Array.isArray(attendeesList)) {
		return [];
	}

	return attendeesList
		.filter(
			(attendee): attendee is Record<string, any> =>
				attendee !== null && typeof attendee === 'object',
		)
		.map((attendee) => ({
			id: String(attendee.id ?? ''),
			name: attendee.name || attendee.display_name || 'Unknown Attendee',
		}));
}

/**
 * Extracts thumbnail URL from various possible Una event properties
 */
function extractThumbnail(unaEvent: UnaEvent): string | null {
	if (unaEvent.images?.thumbnail) {
		return unaEvent.images.thumbnail;
	}

	if (unaEvent.thumbnail) {
		return unaEvent.thumbnail;
	}

	if (unaEvent.cover?.storage) {
		try {
			// Use API base URL from environment config instead of hardcoded URL
			const baseUrl = config.API_URL.replace(/\/+$/, ''); // Remove trailing slashes
			// Use URL constructor for safety and proper encoding
			const url = new URL(`/s/${encodeURIComponent(unaEvent.cover.storage)}`, baseUrl);
			return url.toString();
		} catch (error) {
			adapterLogger.warn('Invalid URL construction for cover image', {
				storage: unaEvent.cover.storage,
			});
			return null;
		}
	}

	return null;
}

/**
 * Extracts URI from a URL (last segment of path)
 */
function extractUriFromUrl(url: string | undefined | null): string | null {
	if (!url) {
		return null;
	}

	const segments = url.split('/').filter(Boolean);
	return segments.length > 0 ? segments[segments.length - 1] : null;
}

/**
 * Converts a raw Una event to the app's RawEventData format
 *
 * This is an intermediate step before using the existing transformEventData
 * function to get the final EventData format.
 *
 * @param unaEvent - Raw Una event from the API
 * @returns RawEventData format compatible with existing transformers
 * @throws Error if no valid start date found
 */
export function unaEventToRawEventData(unaEvent: UnaEvent): RawEventData {
	const startDate = extractStartDate(unaEvent);

	// Throw error if no valid start date - don't mask data quality issues
	if (!startDate) {
		adapterLogger.error('Event missing valid start date', {
			eventId: unaEvent.id,
			title: unaEvent.title || unaEvent.event_name,
			dateFields: {
				date_start: unaEvent.date_start,
				added: unaEvent.added,
				date: unaEvent.date,
			},
		});
		throw new Error(`Event ${unaEvent.id} has no valid start date`);
	}

	const endDate = extractEndDate(unaEvent);

	return {
		id: String(unaEvent.id),
		title: unaEvent.title || unaEvent.event_name || 'Untitled Event',
		description: unaEvent.description || '',
		date: startDate,
		start_date: startDate,
		end_date: endDate,
		location: unaEvent.location || '',
		virtual: Boolean(unaEvent.virtual),
		attendees: typeof unaEvent.attendees === 'number' ? unaEvent.attendees : 0,
		attendeesList: transformAttendeesList(unaEvent.attendeesList),
		status: extractRSVPStatus(unaEvent),
		thumbnail: extractThumbnail(unaEvent),
		organizer: unaEvent.author_data?.display_name || null,
		url: unaEvent.url || null,
		uri: extractUriFromUrl(unaEvent.url),
	};
}

/**
 * Adapts a raw Una event to the app's EventData format
 *
 * @param unaEvent - Raw Una event from API
 * @returns EventData format for app components
 */
export function adaptUnaEvent(unaEvent: UnaEvent): EventData {
	const rawEventData = unaEventToRawEventData(unaEvent);
	return transformEventData(rawEventData);
}

/**
 * Adapts multiple Una events to the app's EventData format
 *
 * @param unaEvents - Array of raw Una events
 * @returns Array of EventData for app components
 */
export function adaptUnaEvents(unaEvents: UnaEvent[]): EventData[] {
	return unaEvents.map(adaptUnaEvent);
}

/**
 * Processes the complete Una API response and extracts all events
 *
 * Handles various response structures:
 * - Direct array of containers
 * - Response object with nested events
 * - Una API structure with data array containing browse containers
 *
 * @param response - Complete Una API response from middleware API
 * @returns Array of EventData ready for use in the app
 */
export function processUnaApiResponse(response: unknown): EventData[] {
	if (!response) {
		adapterLogger.warn('No response data provided');
		return [];
	}

	try {
		// Normalize response to get the data array
		const normalizedData = normalizeResponseStructure(response);

		// Extract all Una events from containers
		const unaEvents = extractAllEventsFromContainers(normalizedData);

		// Transform Una events to app EventData format
		const eventData = unaEvents
			.map((event) => {
				try {
					return adaptUnaEvent(event);
				} catch (error) {
					adapterLogger.error('Error transforming event', { eventId: event.id, error });
					return null;
				}
			})
			.filter((event): event is EventData => event !== null);

		if (__DEV__) {
			adapterLogger.debug(`Successfully processed ${eventData.length} events`);
		}

		return eventData;
	} catch (error) {
		adapterLogger.error('Error processing Una API response', { error });
		return [];
	}
}

/**
 * Normalizes various response structures to get the containers array
 */
function normalizeResponseStructure(response: unknown): unknown[] {
	if (Array.isArray(response)) {
		return response;
	}

	if (typeof response !== 'object' || response === null) {
		return [];
	}

	const responseObj = response as Record<string, any>;

	// Check for nested events property
	if (responseObj.events) {
		return normalizeResponseStructure(responseObj.events);
	}

	// Check for data array (Una API structure)
	if (Array.isArray(responseObj.data)) {
		return responseObj.data;
	}

	// Single object that might be a container
	return [response];
}

/**
 * Extracts all events from an array of containers
 */
function extractAllEventsFromContainers(containers: unknown[]): UnaEvent[] {
	const allEvents: UnaEvent[] = [];

	for (const container of containers) {
		if (isBrowseContainer(container)) {
			const events = extractEventsFromContainer(container);
			allEvents.push(...events);
		}
	}

	return allEvents;
}

/**
 * Type guard to check if an object is a valid Una event
 *
 * @param obj - Object to check
 * @returns True if the object appears to be a valid Una event
 */
export function isUnaEvent(obj: unknown): obj is UnaEvent {
	return (
		typeof obj === 'object' &&
		obj !== null &&
		'id' in obj &&
		(typeof obj.id === 'string' || typeof obj.id === 'number')
	);
}
