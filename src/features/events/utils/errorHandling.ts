/**
 * Error Handling Utilities for Events Feature
 *
 * Standardized error handling for event API calls
 */

import { logger } from '@/utils/logger';
import type { ApiResponse } from '@/api/api';

const errorLogger = logger.child('EventErrorHandling');

/**
 * Type guard to check if response is an error response
 */
export function isApiErrorResponse(data: any): data is { success: false; message?: string } {
	return data && typeof data === 'object' && data.success === false;
}

/**
 * Handle API errors and convert to user-friendly messages
 */
export function handleApiError(error: unknown, defaultMessage: string): string {
	if (error instanceof Error) {
		errorLogger.error('API Error', { message: error.message });
		return error.message;
	}

	if (typeof error === 'string') {
		errorLogger.error('API Error', { message: error });
		return error;
	}

	errorLogger.error('Unknown API Error', { error });
	return defaultMessage;
}

/**
 * Unified error handler for event API responses
 * Checks if response indicates failure and returns appropriate error message
 *
 * @param response - API response to check
 * @param defaultError<PERSON>ey - Default translation key or message if no specific error provided
 * @returns Error message if response failed, null if success
 */
export function handleEventApiResponse<T>(
	response: ApiResponse<T>,
	defaultErrorKey: string,
): string | null {
	if (isApiErrorResponse(response.data) && response.data.success === false) {
		const errorMessage = response.data.message || defaultErrorKey;
		errorLogger.debug('API response error', { errorMessage, defaultErrorKey });
		return errorMessage;
	}
	return null;
}
