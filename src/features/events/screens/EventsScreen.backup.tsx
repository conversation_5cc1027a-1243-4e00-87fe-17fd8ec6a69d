import React from 'react';
import { StyleSheet, SafeAreaView, Text, View } from 'react-native';

/**
 * Events Screen Component
 *
 * Main screen for displaying events with sections based layout
 */
export interface EventsScreenProps {
	initialFilter?: 'your' | 'group' | 'discover' | 'past';
}

export const EventsScreen: React.FC = () => {
	console.log('[EventsScreen] Rendering minimal version');

	return (
		<SafeAreaView style={styles.container}>
			<View style={styles.content}>
				<Text style={styles.title}>Events Screen</Text>
				<Text style={styles.subtitle}>Minimal version - building up...</Text>
			</View>
		</SafeAreaView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#f5f5f5',
	},
	content: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 20,
	},
	title: {
		fontSize: 24,
		fontWeight: 'bold',
		marginBottom: 10,
		color: '#333',
	},
	subtitle: {
		fontSize: 16,
		color: '#666',
		textAlign: 'center',
	},
});

export default EventsScreen;
