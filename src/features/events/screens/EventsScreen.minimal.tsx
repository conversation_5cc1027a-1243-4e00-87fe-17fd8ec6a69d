import React from 'react';
import { StyleSheet, SafeAreaView, Text, View } from 'react-native';

/**
 * Events Screen Component - Minimal Version
 *
 * Testing basic rendering without hooks
 */
export const EventsScreen: React.FC = () => {
	console.log('[EventsScreen] Rendering minimal version');

	return (
		<SafeAreaView style={styles.container}>
			<View style={styles.content}>
				<Text style={styles.title}>Events Screen</Text>
				<Text style={styles.subtitle}>Minimal version loaded successfully ✅</Text>
			</View>
		</SafeAreaView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#fff',
	},
	content: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 20,
	},
	title: {
		fontSize: 24,
		fontWeight: 'bold',
		marginBottom: 10,
	},
	subtitle: {
		fontSize: 16,
		color: '#666',
	},
});

export default EventsScreen;
