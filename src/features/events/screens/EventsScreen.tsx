import React, { useEffect, useCallback } from 'react';
import { StyleSheet, SafeAreaView, FlatList } from 'react-native';
import { router } from 'expo-router';
import { EventSection } from '../components';
import { useEventActions } from '../hooks/useEventActions';
import { logger } from '@/utils/logger';

const eventsScreenLogger = logger.child('EventsScreen');

/**
 * Events Screen Component
 *
 * Main screen for displaying events with sections based layout
 */
export interface EventsScreenProps {
	initialFilter?: 'your' | 'group' | 'discover' | 'past';
}

export const EventsScreen: React.FC<EventsScreenProps> = ({ initialFilter } = {}) => {
	// Get event actions from custom hook
	const {
		yourEvents,
		groupEvents,
		discoverEvents,
		pastEvents,
		loadingStates,
		error,
		fetchAllEventCategories,
		fetchEventDetailsById,
		updateEventRSVPStatus,
	} = useEventActions();

	// Load all event categories on initial render
	useEffect(() => {
		eventsScreenLogger.info('EventsScreen mounted, fetching all event categories');
		try {
			fetchAllEventCategories();

			// If initialFilter is provided, scroll to that section
			if (initialFilter) {
				eventsScreenLogger.debug(`Initial filter provided: ${initialFilter}`);
				// You could implement scrolling logic here if needed
			}
		} catch (error) {
			eventsScreenLogger.error('Error calling fetchAllEventCategories:', error);
		}
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []); // Empty deps - only run on mount

	// Separate useEffect for logging state changes without triggering fetches
	useEffect(() => {
		eventsScreenLogger.debug('Current state:', {
			yourEvents: yourEvents?.length || 0,
			groupEvents: groupEvents?.length || 0,
			discoverEvents: discoverEvents?.length || 0,
			pastEvents: pastEvents?.length || 0,
			loadingStates,
		});
	}, [yourEvents, groupEvents, discoverEvents, pastEvents, loadingStates, error]);

	// Handle event press to view details
	const handleEventPress = useCallback(
		(eventId: string) => {
			// Fetch event details first to pre-populate the store
			fetchEventDetailsById(eventId);

			// Navigate to event detail screen using Expo Router with correct path to preserve navigation hierarchy
			router.push(`/(tabs)/more/events/${eventId}`);
		},
		[fetchEventDetailsById],
	);

	// Handle RSVP status change
	const handleStatusChange = useCallback(
		(eventId: string, status: 'going' | 'interested' | 'not_going') => {
			updateEventRSVPStatus(eventId, status);
		},
		[updateEventRSVPStatus],
	);
	// Handle message button press
	const handleMessagePress = useCallback((eventId: string) => {
		eventsScreenLogger.debug(`Message button pressed for event: ${eventId}`);
	}, []);

	// Handle view all actions for each section
	const handleViewAllYourEvents = useCallback(() => {
		router.push('/(tabs)/more/events/your-events');
	}, []);

	const handleViewAllGroupEvents = useCallback(() => {
		router.push('/(tabs)/more/events/group-events');
	}, []);

	const handleViewAllDiscoverEvents = useCallback(() => {
		router.push('/(tabs)/more/events/discover');
	}, []);

	const handleViewAllPastEvents = useCallback(() => {
		router.push('/(tabs)/more/events/past');
	}, []);

	// Create sections data for FlatList with per-section loading states
	const sections = [
		{
			id: 'your-events',
			title: 'Your Events',
			events: yourEvents,
			onViewAll: handleViewAllYourEvents,
			onDiscoverPress: handleViewAllDiscoverEvents,
			loading: loadingStates.yourEvents,
		},
		{
			id: 'group-events',
			title: 'My Groups & Communities',
			events: groupEvents,
			onViewAll: handleViewAllGroupEvents,
			loading: loadingStates.groupEvents,
		},
		{
			id: 'discover-events',
			title: 'Discover Events',
			events: discoverEvents,
			onViewAll: handleViewAllDiscoverEvents,
			loading: loadingStates.discoverEvents,
		},
		{
			id: 'past-events',
			title: 'Past Events',
			events: pastEvents,
			onViewAll: handleViewAllPastEvents,
			loading: loadingStates.pastEvents,
		},
	];

	const renderSection = ({ item }: { item: (typeof sections)[0] }) => (
		<EventSection
			title={item.title}
			events={item.events}
			onViewAll={item.onViewAll}
			onEventPress={handleEventPress}
			onStatusChange={handleStatusChange}
			onMessagePress={handleMessagePress}
			onDiscoverPress={item.onDiscoverPress}
			loading={item.loading}
			error={error}
		/>
	);

	return (
		<SafeAreaView style={styles.container}>
			<FlatList
				data={sections}
				renderItem={renderSection}
				keyExtractor={(item) => item.id}
				contentContainerStyle={styles.scrollView}
				showsVerticalScrollIndicator={false}
				scrollEnabled={true}
				nestedScrollEnabled={true}
			/>
		</SafeAreaView>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#f5f5f5',
	},
	scrollView: {
		paddingTop: 16,
		paddingBottom: 24, // Add bottom padding for better scrolling experience
	},
});

export default EventsScreen;
