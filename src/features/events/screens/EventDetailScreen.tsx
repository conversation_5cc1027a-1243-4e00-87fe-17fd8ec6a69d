import React, { useEffect, useMemo, useState } from 'react';
import {
	View,
	Text,
	StyleSheet,
	ScrollView,
	Image,
	ActivityIndicator,
	SafeAreaView,
	StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import useEventActions from '../hooks/useEventActions';
import { ErrorView } from '../components';
import CoverImageFallback from '../components/CoverImageFallback';
import RSVPButtonGroup from '../components/RSVPButtonGroup';
import { formatEventDate, formatEventTime } from '../model/transformers';

// Define component props interface
interface EventDetailScreenProps {
	eventId: string;
}

/**
 * Event Detail Screen Component
 *
 * Displays detailed information about a specific event
 */
function EventDetailScreen({ eventId }: EventDetailScreenProps) {
	const { t } = useTranslation();
	const { selectedEvent, loadingStates, error, fetchEventDetailsById, updateEventRSVPStatus } =
		useEventActions();
	const [coverImageError, setCoverImageError] = useState(false);

	// Use specific loading state for selected event
	const loading = loadingStates.selectedEvent;

	useEffect(() => {
		fetchEventDetailsById(eventId);
	}, [fetchEventDetailsById, eventId]);

	// Handle back button press
	const handleBack = () => {
		router.back();
	};

	// Handle status change
	const handleStatusChange = (status: 'going' | 'interested' | 'not_going') => {
		if (selectedEvent) {
			updateEventRSVPStatus(selectedEvent.id, status);
		}
	};

	// Format dates for display
	const formattedDate = useMemo(() => {
		if (!selectedEvent) return '';
		return formatEventDate(selectedEvent.date, {
			weekday: 'long',
			year: 'numeric',
			month: 'long',
			day: 'numeric',
		});
	}, [selectedEvent]);

	const timeDisplay = useMemo(() => {
		if (!selectedEvent) return '';

		const startTime = selectedEvent.localStartTime || formatEventTime(selectedEvent.date);

		let display = startTime;

		if (selectedEvent.localEndTime) {
			display += ` - ${selectedEvent.localEndTime}`;
		}

		return display;
	}, [selectedEvent]);

	// Loading state - show loading indicator if we're fetching data
	if (loading) {
		return (
			<SafeAreaView style={styles.loadingContainer}>
				<ActivityIndicator size='large' color='#5c6bc0' />
				<Text style={styles.loadingText}>
					{t('features.events.errors.loadingEventDetails')}
				</Text>
			</SafeAreaView>
		);
	}

	// Error state - only show error if loading is complete
	if (error) {
		return (
			<SafeAreaView style={styles.loadingContainer}>
				<ErrorView
					message={error}
					onRetry={() => fetchEventDetailsById(eventId)}
					retryText={t('features.events.errors.tryAgain')}
				/>
			</SafeAreaView>
		);
	}

	// If no event data is available and we're not loading, show not found message
	if (!selectedEvent && !loading) {
		return (
			<SafeAreaView style={styles.loadingContainer}>
				<ErrorView message='Event not found' onRetry={handleBack} retryText='Go Back' />
			</SafeAreaView>
		);
	}

	// If we're still waiting for data but not in loading state, show a placeholder
	if (!selectedEvent) {
		return (
			<SafeAreaView style={styles.loadingContainer}>
				<ActivityIndicator size='small' color='#5c6bc0' />
				<Text style={styles.loadingText}>Preparing event details...</Text>
			</SafeAreaView>
		);
	}

	// Default placeholder image for events without thumbnails
	const coverImage = selectedEvent.thumbnail
		? { uri: selectedEvent.thumbnail }
		: require('@assets/images/event-placeholder.png');

	return (
		<SafeAreaView style={styles.container}>
			<StatusBar barStyle='light-content' />

			{/* Header space - back button removed */}

			<ScrollView style={styles.scrollView}>
				{/* Cover Image */}
				{coverImageError ? (
					<CoverImageFallback height={250} title={selectedEvent.title} />
				) : (
					<Image
						source={coverImage}
						style={styles.coverImage}
						onError={() => setCoverImageError(true)}
					/>
				)}

				{/* Event Title */}
				<View style={styles.titleContainer}>
					<Text style={styles.title}>{selectedEvent.title}</Text>
				</View>

				{/* Event Details */}
				<View style={styles.detailsContainer}>
					{/* Date and Time */}
					<View style={styles.detailRow}>
						<View style={styles.detailIconContainer}>
							<Text style={styles.detailIcon}>📅</Text>
						</View>
						<View style={styles.detailTextContainer}>
							<Text style={styles.detailLabel}>Date</Text>
							<Text style={styles.detailText}>{formattedDate}</Text>
							<Text style={styles.detailText}>{timeDisplay}</Text>
						</View>
					</View>

					{/* Location */}
					<View style={styles.detailRow}>
						<View style={styles.detailIconContainer}>
							<Text style={styles.detailIcon}>
								{selectedEvent.virtual ? '🌐' : '📍'}
							</Text>
						</View>
						<View style={styles.detailTextContainer}>
							<Text style={styles.detailLabel}>
								{t('features.events.labels.location')}
							</Text>
							<Text style={styles.detailText}>
								{selectedEvent.virtual
									? t('features.events.location.virtualEvent')
									: selectedEvent.location}
							</Text>
						</View>
					</View>

					{/* RSVP Status */}
					<View style={styles.rsvpContainer}>
						<Text style={styles.rsvpLabel}>{t('features.events.labels.rsvp')}</Text>
						<RSVPButtonGroup
							currentStatus={selectedEvent.status || 'not_going'}
							onStatusChange={handleStatusChange}
						/>
					</View>

					{/* Description */}
					{selectedEvent.description && (
						<View style={styles.descriptionContainer}>
							<Text style={styles.descriptionLabel}>
								{t('features.events.labels.description')}
							</Text>
							<Text style={styles.descriptionText}>{selectedEvent.description}</Text>
						</View>
					)}
					{/* Attendees */}
					<View style={styles.attendeesContainer}>
						<Text style={styles.attendeesLabel}>
							{t('features.events.labels.attendees')} ({selectedEvent.attendees})
						</Text>

						{selectedEvent.attendeesList.length > 0 ? (
							<ScrollView
								horizontal
								showsHorizontalScrollIndicator={false}
								contentContainerStyle={styles.attendeesList}
							>
								{selectedEvent.attendeesList.map((item) => (
									<View key={item.id} style={styles.attendeeItem}>
										<View style={styles.attendeeAvatar}>
											<Text style={styles.attendeeInitial}>
												{item.name.charAt(0)}
											</Text>
										</View>
										<Text style={styles.attendeeName}>{item.name}</Text>
									</View>
								))}
							</ScrollView>
						) : (
							<Text style={styles.noAttendeesText}>
								{t('features.events.attendees.none')}
							</Text>
						)}
					</View>

					{/* Debug section removed - keeping focus on core feature implementation */}
				</View>
			</ScrollView>
		</SafeAreaView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#fff',
	},
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#fff',
	},
	loadingText: {
		marginTop: 12,
		fontSize: 16,
		color: '#757575',
	},
	header: {
		position: 'absolute',
		top: 0,
		left: 0,
		right: 0,
		zIndex: 10,
		paddingHorizontal: 16,
		paddingVertical: 16,
	},
	backButton: {
		width: 40,
		height: 40,
		borderRadius: 20,
		backgroundColor: 'rgba(0, 0, 0, 0.3)',
		justifyContent: 'center',
		alignItems: 'center',
	},
	backButtonText: {
		fontSize: 24,
		color: '#fff',
		fontWeight: 'bold',
	},
	scrollView: {
		flex: 1,
	},
	coverImage: {
		width: '100%',
		height: 250,
		resizeMode: 'contain',
		backgroundColor: '#f5f5f5',
	},
	titleContainer: {
		padding: 16,
		backgroundColor: '#fff',
	},
	title: {
		fontSize: 24,
		fontWeight: 'bold',
		color: '#333',
	},
	detailsContainer: {
		padding: 16,
		backgroundColor: '#fff',
	},
	detailRow: {
		flexDirection: 'row',
		marginBottom: 16,
	},
	detailIconContainer: {
		width: 40,
		height: 40,
		borderRadius: 20,
		backgroundColor: '#f0f0f0',
		justifyContent: 'center',
		alignItems: 'center',
		marginRight: 12,
	},
	detailIcon: {
		fontSize: 20,
	},
	detailTextContainer: {
		flex: 1,
		justifyContent: 'center',
	},
	detailLabel: {
		fontSize: 12,
		color: '#757575',
		marginBottom: 4,
	},
	detailText: {
		fontSize: 16,
		color: '#333',
	},
	rsvpContainer: {
		marginVertical: 16,
	},
	rsvpLabel: {
		fontSize: 16,
		fontWeight: 'bold',
		color: '#333',
		marginBottom: 8,
	},
	rsvpButtonsContainer: {
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	rsvpButton: {
		flex: 1,
		paddingVertical: 12,
		marginHorizontal: 4,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: '#5c6bc0',
		alignItems: 'center',
		justifyContent: 'center',
	},
	activeRsvpButton: {
		backgroundColor: '#5c6bc0',
	},
	rsvpButtonText: {
		color: '#5c6bc0',
		fontWeight: '600',
	},
	activeRsvpButtonText: {
		color: '#fff',
	},
	descriptionContainer: {
		marginVertical: 16,
	},
	descriptionLabel: {
		fontSize: 16,
		fontWeight: 'bold',
		color: '#333',
		marginBottom: 8,
	},
	descriptionText: {
		fontSize: 16,
		lineHeight: 24,
		color: '#333',
	},
	attendeesContainer: {
		marginVertical: 16,
	},
	attendeesLabel: {
		fontSize: 16,
		fontWeight: 'bold',
		color: '#333',
		marginBottom: 8,
	},
	attendeesList: {
		paddingVertical: 8,
	},
	attendeeItem: {
		marginRight: 12,
		alignItems: 'center',
	},
	attendeeAvatar: {
		width: 40,
		height: 40,
		borderRadius: 20,
		backgroundColor: '#5c6bc0',
		justifyContent: 'center',
		alignItems: 'center',
		marginBottom: 4,
	},
	attendeeInitial: {
		color: '#fff',
		fontSize: 18,
		fontWeight: 'bold',
	},
	attendeeName: {
		fontSize: 12,
		color: '#333',
		textAlign: 'center',
		width: 60,
	},
	noAttendeesText: {
		fontSize: 14,
		color: '#757575',
		fontStyle: 'italic',
	},
	errorText: {
		fontSize: 16,
		color: '#e53935',
		textAlign: 'center',
		marginBottom: 16,
	},
	retryButton: {
		paddingVertical: 8,
		paddingHorizontal: 16,
		backgroundColor: '#5c6bc0',
		borderRadius: 4,
	},
	retryButtonText: {
		color: '#fff',
		fontWeight: '600',
	},
	// Debug styles removed
});

export default EventDetailScreen;
