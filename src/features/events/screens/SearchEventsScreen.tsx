import React, { useState } from 'react';
import { View, StyleSheet, TextInput, SafeAreaView, FlatList } from 'react-native';
import { router } from 'expo-router';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { EventCard } from '../components';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';

/**
 * Events Search Screen
 *
 * Allows users to search through all events.
 * This is a pure screen component that can be reused in different navigation contexts.
 */
export default function SearchEventsScreen() {
	const [searchQuery, setSearchQuery] = useState('');

	// Get all events from the Redux store
	const { yourEvents, groupEvents, discoverEvents, pastEvents } = useSelector(
		(state: RootState) => state.events,
	);

	// Combine all event lists for searching
	const allEvents = [...yourEvents, ...groupEvents, ...discoverEvents, ...pastEvents];

	// Filter events based on search query
	const filteredEvents =
		searchQuery.length > 0
			? allEvents.filter(
					(event) =>
						event.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
						event.description?.toLowerCase().includes(searchQuery.toLowerCase()),
				)
			: [];

	// Handle selecting an event
	const handleEventPress = (eventId: string) => {
		router.push(`/(tabs)/more/events/${eventId}`);
	};

	return (
		<SafeAreaView style={styles.container}>
			<View style={styles.searchContainer}>
				<TextInput
					style={styles.searchInput}
					placeholder='Search events...'
					value={searchQuery}
					onChangeText={setSearchQuery}
					autoFocus
					returnKeyType='search'
				/>
			</View>

			<FlatList
				data={filteredEvents}
				renderItem={({ item }) => <EventCard event={item} onPress={handleEventPress} />}
				keyExtractor={(item) => item.id}
				contentContainerStyle={styles.eventsList}
				ListEmptyComponent={() => (
					<ThemedView style={styles.emptyContainer}>
						<ThemedText type='default' style={styles.emptyText}>
							{searchQuery.length > 0
								? `No events found for "${searchQuery}"`
								: 'Type to search for events'}
						</ThemedText>
					</ThemedView>
				)}
			/>
		</SafeAreaView>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#f5f5f5',
	},
	searchContainer: {
		padding: 16,
		backgroundColor: 'white',
		borderBottomWidth: 1,
		borderBottomColor: '#eee',
	},
	searchInput: {
		height: 40,
		backgroundColor: '#f0f0f0',
		paddingHorizontal: 16,
		borderRadius: 20,
		fontSize: 16,
	},
	eventsList: {
		padding: 16,
	},
	emptyContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 32,
	},
	emptyText: {
		textAlign: 'center',
		opacity: 0.7,
	},
});
