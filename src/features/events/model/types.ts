/**
 * Events feature data types
 *
 * This file contains type definitions for the Events feature
 * - RawEventData represents the data structure from the API
 * - EventData represents the transformed data structure used in the app
 */

import { EventStatus } from '../types/shared';

/**
 * Raw event data structure from API
 *
 * This interface exactly matches the response from the middleware API
 * based on UnaEventsService.formatSingleEvent()
 */
export interface RawEventData {
	id: string;
	title: string;
	description: string;
	date: string; // ISO 8601 date string
	start_date: string; // ISO 8601 date string (duplicate of date for compatibility)
	end_date: string | null; // ISO 8601 date string or null
	location: string;
	virtual: boolean;
	attendees: number;
	attendeesList: {
		id: string;
		name: string;
	}[];
	status: EventStatus; // From middleware API
	thumbnail: string | null;
	organizer: string | null;
	url: string | null; // Full UNA path to the event
	uri: string | null; // UNA's path identifier
}

/**
 * Transformed event data structure used in the app
 */
export interface EventData {
	id: string;
	title: string;
	startDate: string; // ISO string format for Redux compatibility
	date: string; // Alias for startDate for backward compatibility
	endDate?: string; // ISO string for end date
	location: string;
	virtual: boolean;
	attendees: number;
	attendeesList: { id: string; name: string }[];
	status?: EventStatus;
	thumbnail?: string;
	description?: string;
	localStartTime?: string;
	localEndTime?: string;
}

// Event types are imported from shared.ts
