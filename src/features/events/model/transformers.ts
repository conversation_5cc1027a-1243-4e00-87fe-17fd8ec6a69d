/**
 * Event data transformers
 *
 * This file contains functions to transform raw API event data into
 * the application's data model and utility functions for formatting event data.
 */
import { EventData, RawEventData } from './types';
import { logger } from '@/utils/logger';

const transformerLogger = logger.child('EventTransformers');

// Constants for discover events display limit
export const DISCOVER_EVENTS_DISPLAY_LIMIT = 3;

/**
 * Extracted attendee information
 */
interface ParsedAttendees {
	count: number;
	list: { id: string; name: string }[];
}

/**
 * Parse and validate start date from raw event data
 * @param data - Raw event data
 * @returns Parsed Date object and ISO string
 */
function parseStartDate(data: RawEventData): { dateObj: Date; dateISOString: string } {
	try {
		// Try to get the date from either 'start_date' or 'date' field
		const dateString = data.start_date || data.date;

		if (__DEV__) {
			transformerLogger.debug(`Event ${data.id} date fields:`, {
				start_date: data.start_date,
				date: data.date,
				using: dateString,
			});
		}

		// Parse the date
		const dateObj = dateString ? new Date(dateString) : new Date();

		// Validate the date
		if (isNaN(dateObj.getTime())) {
			transformerLogger.warn(`Invalid date format, using current date instead:`, dateString);
			const fallbackDate = new Date();
			return {
				dateObj: fallbackDate,
				dateISOString: fallbackDate.toISOString(),
			};
		}

		return {
			dateObj,
			dateISOString: dateObj.toISOString(),
		};
	} catch (e) {
		transformerLogger.error('Error parsing date:', e);
		const fallbackDate = new Date();
		return {
			dateObj: fallbackDate,
			dateISOString: fallbackDate.toISOString(),
		};
	}
}

/**
 * Parse and validate end date from raw event data
 * @param data - Raw event data
 * @returns Parsed Date object and ISO string, or undefined if not available
 */
function parseEndDate(data: RawEventData): { endDateObj?: Date; endDateISOString?: string } {
	if (!data.end_date) {
		return { endDateObj: undefined, endDateISOString: undefined };
	}

	try {
		const endDateObj = new Date(data.end_date);

		if (isNaN(endDateObj.getTime())) {
			transformerLogger.warn('Invalid end_date format, ignoring:', data.end_date);
			return { endDateObj: undefined, endDateISOString: undefined };
		}

		return {
			endDateObj,
			endDateISOString: endDateObj.toISOString(),
		};
	} catch (e) {
		transformerLogger.error('Error parsing end_date:', e);
		return { endDateObj: undefined, endDateISOString: undefined };
	}
}

/**
 * Format local time strings for event dates
 * @param startDate - Start date object
 * @param endDate - Optional end date object
 * @returns Formatted time strings
 */
function formatLocalTimes(
	startDate: Date,
	endDate?: Date,
): { localStartTime: string; localEndTime?: string } {
	let localStartTime = '';
	let localEndTime: string | undefined;

	try {
		localStartTime = formatEventTime(startDate);
	} catch (e) {
		transformerLogger.error('Error formatting start time:', e);
		localStartTime = '';
	}

	if (endDate) {
		try {
			localEndTime = formatEventTime(endDate);
		} catch (e) {
			transformerLogger.error('Error formatting end time:', e);
			localEndTime = '';
		}
	}

	return { localStartTime, localEndTime };
}

/**
 * Extract and normalize attendee information
 * @param data - Raw event data
 * @returns Normalized attendee count and list
 */
function extractAttendees(data: RawEventData): ParsedAttendees {
	let attendeeCount = 0;
	let attendeesList: { id: string; name: string }[] = [];

	// Handle different data models: attendees could be an array or a number
	if (Array.isArray(data.attendees)) {
		// Case 1: attendees is an array of objects with id and name
		attendeeCount = data.attendees.length;
		attendeesList = data.attendees.map((attendee) => ({
			id: attendee.id,
			name: attendee.name,
		}));
	} else if (typeof data.attendees === 'number') {
		// Case 2: attendees is a number and attendeesList might be a separate property
		attendeeCount = data.attendees;
		attendeesList = Array.isArray(data.attendeesList) ? data.attendeesList : [];
	}

	return {
		count: attendeeCount,
		list: attendeesList,
	};
}

/**
 * Transform raw API event data to application event model
 *
 * This is the main transformation function that orchestrates the conversion
 * of raw API data into the application's EventData format.
 *
 * @param data - Raw event data from API
 * @returns Transformed event data for application use
 */
export function transformEventData(data: RawEventData): EventData {
	// Parse dates
	const { dateObj, dateISOString } = parseStartDate(data);
	const { endDateObj, endDateISOString } = parseEndDate(data);

	// Format times
	const { localStartTime, localEndTime } = formatLocalTimes(dateObj, endDateObj);

	// Format location
	const location = formatEventLocation(data.location);

	// Extract attendees
	const { count: attendeeCount, list: attendeesList } = extractAttendees(data);

	// Get RSVP status
	const status = data.status;
	if (__DEV__) {
		transformerLogger.debug(`Event ${data.id} status:`, { status });
	}

	// Extract thumbnail
	const thumbnail = data.thumbnail;

	// Extract description (Una CMS provides plain text)
	const description = data.description || '';

	return {
		id: data.id,
		title: data.title,
		startDate: dateISOString,
		date: dateISOString, // Keep for backward compatibility
		location,
		virtual: data.virtual,
		attendees: attendeeCount,
		attendeesList,
		status,
		thumbnail: thumbnail || undefined,
		description,
		localStartTime,
		localEndTime,
		endDate: endDateISOString,
	};
}

/**
 * Format event location for display
 * @param location - Raw location string or location object
 * @returns Formatted location string
 */
export function formatEventLocation(
	location:
		| string
		| {
				address?: string;
				city?: string;
				state?: string;
				country?: string;
				zip?: string;
				virtual?: boolean;
		  },
): string {
	// Preserve empty strings from API to match what UNA provides
	if (location === undefined || location === null) {
		return '';
	}

	// Handle object format
	if (typeof location === 'object') {
		// Check if it's a virtual event
		if (location.virtual === true) {
			return 'Virtual Event'; // TODO: Add translation support
		}

		// Build location string from parts
		const parts: string[] = [];
		if (location.address) parts.push(location.address);
		if (location.city) parts.push(location.city);
		if (location.state) parts.push(location.state);
		if (location.country) parts.push(location.country);

		return parts.join(', ');
	}

	// Clean up the location string
	return location.trim();
}

/**
 * Format event date according to locale settings
 * @param date - Event date
 * @param options - Date formatting options
 * @returns Formatted date string
 */
export function formatEventDate(
	date: Date | string,
	options: Intl.DateTimeFormatOptions = {
		weekday: 'short',
		year: 'numeric',
		month: 'short',
		day: 'numeric',
	},
): string {
	// Convert string to Date if needed
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
		throw new Error('Invalid date provided to formatEventDate');
	}

	return dateObj.toLocaleDateString(undefined, options);
}

/**
 * Format event time according to locale settings
 * @param date - Event date/time
 * @returns Formatted time string
 */
export function formatEventTime(
	date: Date | string,
	options: Intl.DateTimeFormatOptions = {
		hour: '2-digit',
		minute: '2-digit',
	},
): string {
	// Convert string to Date if needed
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
		throw new Error('Invalid date provided to formatEventTime');
	}

	return dateObj.toLocaleTimeString(undefined, options);
}

/**
 * Format attendee count for display
 * @param count - Number of attendees
 * @param t - Translation function
 * @returns Formatted attendee count string
 */
export function formatAttendeeCount(
	count: number,
	t: (key: string, options?: any) => string,
): string {
	if (count === 0) return t('features.events.attendees.none');
	if (count === 1) return t('features.events.attendees.one');
	return t('features.events.attendees.many', { count });
}

/**
 * Format event day of week for display
 * @param date - Event date
 * @returns Day of week string (e.g., "Monday")
 */
export function formatEventDayOfWeek(date: Date | string): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
		throw new Error('Invalid date provided to formatEventDayOfWeek');
	}

	return dateObj.toLocaleDateString('en-US', { weekday: 'long' });
}

/**
 * Format event full date for display
 * @param date - Event date
 * @returns Full date string (e.g., "January 15, 2025")
 */
export function formatEventFullDate(date: Date | string): string {
	const dateObj = typeof date === 'string' ? new Date(date) : date;

	if (!(dateObj instanceof Date) || isNaN(dateObj.getTime())) {
		throw new Error('Invalid date provided to formatEventFullDate');
	}

	return dateObj.toLocaleDateString('en-US', {
		month: 'long',
		day: 'numeric',
		year: 'numeric',
	});
}

/**
 * Generate status label from status code
 * @param status - Event RSVP status
 * @returns Human-readable status label
 */
export function generateStatusLabel(status?: string, t?: (key: string) => string): string {
	if (!t) {
		// Fallback for when translation function is not available
		switch (status) {
			case 'going':
				return 'Going';
			case 'interested':
				return 'Interested';
			case 'not_going':
				return 'Not Going';
			default:
				return 'RSVP';
		}
	}

	switch (status) {
		case 'going':
			return t('features.events.rsvp.going');
		case 'interested':
			return t('features.events.rsvp.interested');
		case 'not_going':
			return t('features.events.rsvp.notGoing');
		default:
			return t('features.events.rsvp.buttonLabel');
	}
}

/**
 * Validate event data structure
 * @param data - Event data to validate
 * @param applyDefaults - Whether to apply defaults for missing fields
 * @returns Validated event data if applyDefaults is true, otherwise boolean true, or throws error
 */
export function validateEventData(data: EventData, applyDefaults = false): EventData | boolean {
	if (!data.id) {
		throw new Error('Event ID is required');
	}

	if (!data.title) {
		throw new Error('Event title is required');
	}

	// Check for valid date string or Date object
	if (!data.date) {
		throw new Error('Valid event date is required');
	}

	// Date should always be a string now in the Redux store
	if (typeof data.date !== 'string') {
		throw new Error('Date must be a string in ISO format');
	}

	// If it's a string, try to parse it
	if (typeof data.date === 'string') {
		try {
			const dateCheck = new Date(data.date);
			if (isNaN(dateCheck.getTime())) {
				throw new Error('Valid event date is required');
			}
		} catch {
			throw new Error('Valid event date is required');
		}
	}

	if (!data.location) {
		throw new Error('Event location is required');
	}

	if (typeof data.virtual !== 'boolean') {
		if (applyDefaults) {
			// Convert truthy/falsy values to boolean
			data.virtual = Boolean(data.virtual);
		} else {
			throw new Error('Event virtual status must be a boolean');
		}
	}

	// Apply defaults for missing fields if requested
	if (applyDefaults) {
		// Normalize attendees to number
		let normalizedAttendees = 0;
		if (typeof data.attendees === 'string') {
			normalizedAttendees = parseInt(data.attendees, 10) || 0;
		} else if (typeof data.attendees === 'number') {
			normalizedAttendees = data.attendees;
		}

		return {
			...data,
			title: data.title.trim(),
			attendees: normalizedAttendees,
			attendeesList: data.attendeesList ?? [],
			description: data.description ?? '',
		};
	}

	return true;
}
