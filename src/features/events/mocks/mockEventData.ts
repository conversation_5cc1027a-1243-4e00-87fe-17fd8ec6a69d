/**
 * Mock Event Data for Development and Demos
 *
 * This file contains mock events to demonstrate the Events feature
 * with realistic data across all event categories.
 */

import { EventData } from '../model/types';

/**
 * Generate a date string in the future (for upcoming events)
 */
const getFutureDate = (daysFromNow: number): string => {
	const date = new Date();
	date.setDate(date.getDate() + daysFromNow);
	return date.toISOString();
};

/**
 * Generate a date string in the past (for past events)
 */
const getPastDate = (daysAgo: number): string => {
	const date = new Date();
	date.setDate(date.getDate() - daysAgo);
	return date.toISOString();
};

/**
 * Mock events for "Your Events" section
 */
export const MOCK_YOUR_EVENTS: EventData[] = [
	{
		id: 'mock-your-1',
		title: 'Mentor Matchup Event',
		description:
			'Connect with experienced mentors in your field and build lasting professional relationships.',
		date: getFutureDate(15),
		startDate: getFutureDate(15),
		location: 'Downtown Learning Hub',
		virtual: false,
		attendees: 101,
		attendeesList: [],
		status: 'going',
		thumbnail: undefined,
	},
	{
		id: 'mock-your-2',
		title: 'Product Design Workshop',
		description: 'Hands-on workshop covering modern product design principles and tools.',
		date: getFutureDate(8),
		startDate: getFutureDate(8),
		location: 'Innovation Center',
		virtual: false,
		attendees: 45,
		attendeesList: [],
		status: 'going',
		thumbnail: undefined,
	},
	{
		id: 'mock-your-3',
		title: 'Weekly Team Standup',
		description: 'Regular sync-up for project updates and collaboration.',
		date: getFutureDate(2),
		startDate: getFutureDate(2),
		location: 'Virtual',
		virtual: true,
		attendees: 12,
		attendeesList: [],
		status: 'going',
		thumbnail: undefined,
	},
];

/**
 * Mock events for "My Groups & Communities" section
 */
export const MOCK_GROUP_EVENTS: EventData[] = [
	{
		id: 'mock-group-1',
		title: 'Creative Coaching Strategies',
		description: 'Learn innovative approaches to coaching and personal development.',
		date: getFutureDate(20),
		startDate: getFutureDate(20),
		location: 'Virtual',
		virtual: true,
		attendees: 189,
		attendeesList: [],
		status: 'interested',
		thumbnail: undefined,
	},
	{
		id: 'mock-group-2',
		title: 'Community Leadership Summit',
		description: 'Annual gathering of community leaders to share insights and strategies.',
		date: getFutureDate(30),
		startDate: getFutureDate(30),
		location: 'City Convention Center',
		virtual: false,
		attendees: 250,
		attendeesList: [],
		status: 'interested',
		thumbnail: undefined,
	},
	{
		id: 'mock-group-3',
		title: 'Tech Talk Tuesday',
		description:
			'Weekly presentation series featuring the latest in technology and innovation.',
		date: getFutureDate(5),
		startDate: getFutureDate(5),
		location: 'Virtual',
		virtual: true,
		attendees: 78,
		attendeesList: [],
		status: 'interested',
		thumbnail: undefined,
	},
	{
		id: 'mock-group-4',
		title: 'Networking Mixer',
		description: 'Casual networking event for professionals across all industries.',
		date: getFutureDate(12),
		startDate: getFutureDate(12),
		location: 'Riverside Cafe',
		virtual: false,
		attendees: 65,
		attendeesList: [],
		status: undefined,
		thumbnail: undefined,
	},
];

/**
 * Mock events for "Discover Events" section
 */
export const MOCK_DISCOVER_EVENTS: EventData[] = [
	{
		id: 'mock-discover-1',
		title: 'Peer Review Roundtable',
		description: 'Collaborative session for peer feedback and professional growth.',
		date: getFutureDate(18),
		startDate: getFutureDate(18),
		location: 'Central Park Learning Space',
		virtual: false,
		attendees: 189,
		attendeesList: [],
		status: undefined,
		thumbnail: undefined,
	},
	{
		id: 'mock-discover-2',
		title: 'Data Science Bootcamp',
		description:
			'Intensive workshop covering data analysis, machine learning, and visualization.',
		date: getFutureDate(25),
		startDate: getFutureDate(25),
		location: 'Tech Campus Building A',
		virtual: false,
		attendees: 156,
		attendeesList: [],
		status: undefined,
		thumbnail: undefined,
	},
	{
		id: 'mock-discover-3',
		title: 'Wellness & Mindfulness Session',
		description: 'Morning session focused on mental health and workplace wellness.',
		date: getFutureDate(10),
		startDate: getFutureDate(10),
		location: 'Virtual',
		virtual: true,
		attendees: 92,
		attendeesList: [],
		status: undefined,
		thumbnail: undefined,
	},
	{
		id: 'mock-discover-4',
		title: 'Startup Pitch Night',
		description:
			'Watch innovative startups pitch their ideas to investors and community members.',
		date: getFutureDate(22),
		startDate: getFutureDate(22),
		location: 'Innovation Hub',
		virtual: false,
		attendees: 134,
		attendeesList: [],
		status: undefined,
		thumbnail: undefined,
	},
];

/**
 * Mock events for "Past Events" section
 */
export const MOCK_PAST_EVENTS: EventData[] = [
	{
		id: 'mock-past-1',
		title: 'Fall/Winter Learning Coach Symposium',
		description:
			'Quarterly symposium bringing together learning coaches from across the region.',
		date: getPastDate(5),
		startDate: getPastDate(5),
		location: 'Virtual',
		virtual: true,
		attendees: 189,
		attendeesList: [],
		status: 'going', // Past events that were attended still show as 'going'
		thumbnail: undefined,
	},
	{
		id: 'mock-past-2',
		title: 'Annual Conference 2024',
		description: 'Our biggest event of the year with keynote speakers and breakout sessions.',
		date: getPastDate(45),
		startDate: getPastDate(45),
		location: 'Grand Conference Hall',
		virtual: false,
		attendees: 512,
		attendeesList: [],
		status: 'going',
		thumbnail: undefined,
	},
	{
		id: 'mock-past-3',
		title: 'Project Kickoff Meeting',
		description: 'Initial planning session for the new initiative.',
		date: getPastDate(10),
		startDate: getPastDate(10),
		location: 'Office Building 3',
		virtual: false,
		attendees: 28,
		attendeesList: [],
		status: 'going',
		thumbnail: undefined,
	},
	{
		id: 'mock-past-4',
		title: 'Social Impact Workshop',
		description: 'Discussion and planning session focused on community impact initiatives.',
		date: getPastDate(20),
		startDate: getPastDate(20),
		location: 'Community Center',
		virtual: false,
		attendees: 67,
		attendeesList: [],
		status: 'going',
		thumbnail: undefined,
	},
];

/**
 * Get all mock events combined
 */
export const ALL_MOCK_EVENTS: EventData[] = [
	...MOCK_YOUR_EVENTS,
	...MOCK_GROUP_EVENTS,
	...MOCK_DISCOVER_EVENTS,
	...MOCK_PAST_EVENTS,
];

/**
 * Check if mock data should be used (dev environment only)
 */
export const shouldUseMockData = (): boolean => {
	return __DEV__ && process.env.EXPO_PUBLIC_ENV_NAME === 'dev';
};

/**
 * Use mock data as fallback when API returns empty results
 * Only active in dev environment
 */
export const useMockDataFallback = (apiData: EventData[], mockData: EventData[]): EventData[] => {
	if (!shouldUseMockData()) {
		return apiData;
	}

	// If API returned data, use it. Otherwise use mock data as fallback.
	const result = apiData.length > 0 ? apiData : mockData;

	if (__DEV__ && apiData.length === 0) {
		console.log('🎭 Using mock data fallback -', mockData.length, 'events');
	}

	return result;
};
