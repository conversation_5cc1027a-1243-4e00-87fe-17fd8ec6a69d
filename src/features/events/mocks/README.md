# Mock Event Data

This directory contains mock data for the Events feature, designed for development and demo purposes.

## Usage

### Enabling Mock Data

Mock data is **automatically enabled** when running in the `dev` environment. Set this in your `.env` file:

```bash
EXPO_PUBLIC_ENV_NAME=dev
```

**Note:** Mock data only works in development mode (`__DEV__` must be true).

### Disabling Mock Data

To disable mock data fallback, change the environment to anything other than `dev`:

```bash
EXPO_PUBLIC_ENV_NAME=production
```

Or comment out the line to use default environment settings.

## Mock Data Categories

The mock data is organized into four categories matching the Events screen sections:

### 1. **Your Events** (3 events)
Events the user has RSVP'd to or is attending:
- Mentor Matchup Event
- Product Design Workshop  
- Weekly Team Standup

### 2. **My Groups & Communities** (4 events)
Events from groups/communities the user is a member of:
- Creative Coaching Strategies
- Community Leadership Summit
- Tech Talk Tuesday
- Networking Mixer

### 3. **Discover Events** (4 events)
Recommended/discoverable events:
- Peer Review Roundtable
- Data Science Bootcamp
- Wellness & Mindfulness Session
- Startup Pitch Night

### 4. **Past Events** (4 events)
Historical events the user attended:
- Fall/Winter Learning Coach Symposium
- Annual Conference 2024
- Project Kickoff Meeting
- Social Impact Workshop

## Data Structure

Each mock event follows the `EventData` interface:

```typescript
{
  id: string;
  title: string;
  description?: string;
  date: string;           // ISO date string
  startDate: string;      // ISO date string
  location: string;
  virtual: boolean;
  attendees: number;
  attendeesList: Array<{ id: string; name: string }>;
  status?: 'going' | 'interested' | 'not_going';
  thumbnail?: string;
}
```

## Demo Benefits

Using mock data allows you to:

1. ✅ **Demo without backend dependencies** - Show features without needing API access
2. ✅ **Consistent demo experience** - Same data every time for reliable demos
3. ✅ **Populated UI states** - Never show empty states in demos
4. ✅ **Offline development** - Work on UI without network connectivity
5. ✅ **Fast iteration** - No API delays during development

## Customization

To modify mock events, edit `/Users/<USER>/dev/stride/lcc/src/features/events/mocks/mockEventData.ts`.

### Adding New Mock Events

```typescript
export const MOCK_YOUR_EVENTS: EventData[] = [
  // ... existing events
  {
    id: 'mock-new-event',
    title: 'New Event Title',
    description: 'Event description',
    date: getFutureDate(10),
    startDate: getFutureDate(10),
    location: 'Event Location',
    virtual: false,
    attendees: 50,
    attendeesList: [],
    status: 'going',
    thumbnail: undefined
  }
];
```

### Date Helpers

- `getFutureDate(daysFromNow)` - Creates a date N days in the future
- `getPastDate(daysAgo)` - Creates a date N days in the past

## Important Notes

- ⚠️ **Development Only**: Mock data is automatically disabled in production builds
- ⚠️ **Environment-Based**: Automatically enabled when `EXPO_PUBLIC_ENV_NAME=dev`
- ⚠️ **Fallback Strategy**: Mock data is used ONLY when API returns empty results
- ⚠️ **Smart Behavior**: If API returns data, it's used. Mock data fills empty sections for demos
