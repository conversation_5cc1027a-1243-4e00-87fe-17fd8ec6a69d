import {
	createSlice,
	createAsyncThunk,
	PayloadAction,
	ActionReducerMapBuilder,
} from '@reduxjs/toolkit';
import { eventService } from '@/services/EventService';
import type { EventData } from './model/types';
import { LoadingState } from './types/shared';
import { processUnaApiResponse, adaptUnaEvent } from './adapters/unaEventAdapter';
import { logger } from '@/utils/logger';
import {
	MOCK_YOUR_EVENTS,
	MOCK_GROUP_EVENTS,
	MOCK_DISCOVER_EVENTS,
	MOCK_PAST_EVENTS,
} from './mocks/mockEventData';
import { handleApiError } from './utils/errorHandling';

const eventsLogger = logger.child('EventsSlice');

// Helper functions
function useMockDataFallback(events: EventData[], mockData: EventData[]): EventData[] {
	if (__DEV__ && events.length === 0) {
		eventsLogger.info('Using mock data fallback');
		return mockData;
	}
	return events;
}

function formatErrorMessage(error: any, defaultMessage: string): string {
	return handleApiError(error, defaultMessage);
}

// Type definitions have been moved to shared.ts

// Define per-category loading states for better UX
interface LoadingStates {
	yourEvents: LoadingState;
	groupEvents: LoadingState;
	discoverEvents: LoadingState;
	pastEvents: LoadingState;
	selectedEvent: LoadingState;
	global: LoadingState; // For operations that affect all categories
}

// Define the state interface
interface EventsState {
	yourEvents: EventData[];
	groupEvents: EventData[];
	discoverEvents: EventData[];
	pastEvents: EventData[];
	selectedEvent: EventData | null;
	loadingStates: LoadingStates;
	error: string | null;
}

// Initialize state
const initialState: EventsState = {
	yourEvents: [],
	groupEvents: [],
	discoverEvents: [],
	pastEvents: [],
	selectedEvent: null,
	loadingStates: {
		yourEvents: 'idle',
		groupEvents: 'idle',
		discoverEvents: 'idle',
		pastEvents: 'idle',
		selectedEvent: 'idle',
		global: 'idle',
	},
	error: null,
};

// Helper function to log API responses with sanitization
const logApiResponse = (type: string, response: any) => {
	const sanitized = response?.data
		? {
				success: response.data.success,
				type: response.data.type,
				has_events: !!response.data.events,
				status: response.status,
			}
		: { error: 'No data' };

	eventsLogger.debug(`${type} API Response:`, sanitized);
};

// Fetch events for "Your Events" section
export const fetchYourEvents = createAsyncThunk(
	'events/fetchYourEvents',
	async (_, { rejectWithValue }) => {
		try {
			eventsLogger.info('Fetching your events');

			const response = await eventService.fetchEvents('current');
			logApiResponse('Your Events', response);

			// Process raw API response using the adapter
			const transformedEvents = processUnaApiResponse(response.data?.events || { data: [] });
			eventsLogger.debug(`Your Events count: ${transformedEvents.length}`);

			// Use mock data as fallback if API returned empty
			return useMockDataFallback(transformedEvents, MOCK_YOUR_EVENTS);
		} catch (error: any) {
			eventsLogger.error('Error fetching your events:', error);
			return rejectWithValue(
				formatErrorMessage(error, 'An error occurred while fetching your events'),
			);
		}
	},
);

// Fetch events for "My Groups & Communities" section
export const fetchGroupEvents = createAsyncThunk(
	'events/fetchGroupEvents',
	async (_, { rejectWithValue }) => {
		try {
			eventsLogger.info('Fetching group events');

			const response = await eventService.fetchEvents('upcoming');
			logApiResponse('Group Events', response);

			// Process raw API response using the adapter
			const transformedEvents = processUnaApiResponse(response.data?.events || { data: [] });
			eventsLogger.debug(`Group Events count: ${transformedEvents.length}`);

			// Use mock data as fallback if API returned empty
			return useMockDataFallback(transformedEvents, MOCK_GROUP_EVENTS);
		} catch (error: any) {
			eventsLogger.error('Error fetching group events:', error);

			// If endpoint doesn't exist (404), use mock data for demo purposes
			if (error.message?.includes('404') && __DEV__) {
				eventsLogger.info('Endpoint not found (404) - using mock data');
				return MOCK_GROUP_EVENTS;
			}

			return rejectWithValue(
				formatErrorMessage(error, 'An error occurred while fetching group events'),
			);
		}
	},
);

// Fetch events for "Discover Events" section
export const fetchDiscoverEvents = createAsyncThunk(
	'events/fetchDiscoverEvents',
	async (_, { rejectWithValue }) => {
		try {
			eventsLogger.info('Fetching discover events');

			const response = await eventService.fetchEvents('discover');
			logApiResponse('Discover Events', response);

			// Process raw API response using the adapter - use only first 3 events for discovery
			const transformedEvents = processUnaApiResponse(
				response.data?.events || { data: [] },
			).slice(0, 3);
			eventsLogger.debug(`Discover Events count: ${transformedEvents.length}`);

			// Use mock data as fallback if API returned empty
			return useMockDataFallback(transformedEvents, MOCK_DISCOVER_EVENTS);
		} catch (error: any) {
			eventsLogger.error('Error fetching discover events:', error);

			// If endpoint doesn't exist (404), use mock data for demo purposes
			if (error.message?.includes('404') && __DEV__) {
				eventsLogger.info('Endpoint not found (404) - using mock data');
				return MOCK_DISCOVER_EVENTS;
			}

			return rejectWithValue(
				formatErrorMessage(error, 'An error occurred while fetching discover events'),
			);
		}
	},
);

// Fetch events for "Past Events" section
export const fetchPastEvents = createAsyncThunk(
	'events/fetchPastEvents',
	async (_, { rejectWithValue }) => {
		try {
			eventsLogger.info('Fetching past events');

			const response = await eventService.fetchEvents('previous');
			logApiResponse('Past Events', response);

			// Process raw API response using the adapter
			const transformedEvents = processUnaApiResponse(response.data?.events || { data: [] });
			eventsLogger.debug(`Past Events count: ${transformedEvents.length}`);

			// Use mock data as fallback if API returned empty
			return useMockDataFallback(transformedEvents, MOCK_PAST_EVENTS);
		} catch (error: any) {
			eventsLogger.error('Error fetching past events:', error);
			return rejectWithValue(
				formatErrorMessage(error, 'An error occurred while fetching past events'),
			);
		}
	},
);

// Fetch all event categories at once
export const fetchAllEventCategories = createAsyncThunk(
	'events/fetchAllCategories',
	async (_, { dispatch }) => {
		eventsLogger.info('Fetching all event categories');
		try {
			await Promise.all([
				dispatch(fetchYourEvents())
					.unwrap()
					.catch((error) => {
						eventsLogger.error('Error in fetchYourEvents:', error);
						return null;
					}),
				dispatch(fetchGroupEvents())
					.unwrap()
					.catch((error) => {
						eventsLogger.error('Error in fetchGroupEvents:', error);
						return null;
					}),
				dispatch(fetchDiscoverEvents())
					.unwrap()
					.catch((error) => {
						eventsLogger.error('Error in fetchDiscoverEvents:', error);
						return null;
					}),
				dispatch(fetchPastEvents())
					.unwrap()
					.catch((error) => {
						eventsLogger.error('Error in fetchPastEvents:', error);
						return null;
					}),
			]);
			eventsLogger.debug('All event category fetches completed');
			return { success: true };
		} catch (error) {
			eventsLogger.error('Error in fetchAllEventCategories:', error);
			throw error;
		}
	},
);

export const fetchEventDetails = createAsyncThunk(
	'events/fetchEventDetails',
	async (eventId: string, { rejectWithValue }) => {
		try {
			const response = await eventService.fetchEventDetails(eventId);

			// Check if the response indicates failure
			const responseData = response.data as
				| { success?: boolean; message?: string }
				| undefined;
			if (responseData && responseData.success === false) {
				return rejectWithValue(responseData.message || 'Failed to fetch event details');
			}

			// Transform raw Una event to app model using adapter
			const transformedEvent = adaptUnaEvent(response.data?.raw_event);

			return transformedEvent;
		} catch (error: any) {
			// Handle different types of errors
			if (error.response?.status === 404) {
				return rejectWithValue('Event not found');
			} else if (error.response?.status === 401) {
				return rejectWithValue('Authentication required');
			} else if (error.response?.status >= 500) {
				return rejectWithValue('Server error. Please try again later.');
			} else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
				return rejectWithValue('No internet connection');
			}

			return rejectWithValue(
				error.message || 'An error occurred while fetching event details',
			);
		}
	},
);

export const updateEventStatus = createAsyncThunk(
	'events/updateEventStatus',
	async (
		{ eventId, status }: { eventId: string; status: 'going' | 'interested' | 'not_going' },
		{ rejectWithValue },
	) => {
		try {
			const response = await eventService.updateEventStatus(eventId, status);

			// Check if the response indicates failure
			const responseData = response.data as
				| { success?: boolean; message?: string }
				| undefined;
			if (responseData && responseData.success === false) {
				return rejectWithValue(responseData.message || 'Failed to update event status');
			}

			return { eventId, status };
		} catch (error: any) {
			// Handle different types of errors
			if (error.response?.status === 404) {
				return rejectWithValue('Event not found');
			} else if (error.response?.status === 401) {
				return rejectWithValue('Authentication required');
			} else if (error.response?.status === 422) {
				return rejectWithValue('Invalid status value');
			} else if (error.response?.status >= 500) {
				return rejectWithValue('Server error. Please try again later.');
			} else if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network')) {
				return rejectWithValue('No internet connection');
			}

			return rejectWithValue(
				error.message || 'An error occurred while updating event status',
			);
		}
	},
);
// Create slice
const eventsSlice = createSlice({
	name: 'events',
	initialState,
	reducers: {
		clearEvents: (state: EventsState) => {
			state.yourEvents = [];
			state.groupEvents = [];
			state.discoverEvents = [];
			state.pastEvents = [];
			state.selectedEvent = null;
			state.loadingStates = {
				yourEvents: 'idle',
				groupEvents: 'idle',
				discoverEvents: 'idle',
				pastEvents: 'idle',
				selectedEvent: 'idle',
				global: 'idle',
			};
			state.error = null;
		},
		clearError: (state: EventsState) => {
			state.error = null;
		},
	},
	extraReducers: (builder: ActionReducerMapBuilder<EventsState>) => {
		builder
			// Start with fetchYourEvents (removed generic fetchEvents which was unused)

			// Handle fetchYourEvents
			.addCase(fetchYourEvents.pending, (state: EventsState) => {
				state.loadingStates.yourEvents = 'loading';
				state.error = null;
			})
			.addCase(
				fetchYourEvents.fulfilled,
				(state: EventsState, action: PayloadAction<EventData[]>) => {
					state.loadingStates.yourEvents = 'succeeded';
					state.yourEvents = action.payload;
				},
			)
			.addCase(fetchYourEvents.rejected, (state: EventsState, action: any) => {
				state.loadingStates.yourEvents = 'failed';
				state.error =
					typeof action.payload === 'string'
						? action.payload
						: 'Failed to fetch your events';
			})

			// Handle fetchGroupEvents
			.addCase(fetchGroupEvents.pending, (state: EventsState) => {
				state.loadingStates.groupEvents = 'loading';
				state.error = null;
			})
			.addCase(
				fetchGroupEvents.fulfilled,
				(state: EventsState, action: PayloadAction<EventData[]>) => {
					state.loadingStates.groupEvents = 'succeeded';
					state.groupEvents = action.payload;
				},
			)
			.addCase(fetchGroupEvents.rejected, (state: EventsState, action: any) => {
				state.loadingStates.groupEvents = 'failed';
				state.error =
					typeof action.payload === 'string'
						? action.payload
						: 'Failed to fetch group events';
			})

			// Handle fetchDiscoverEvents
			.addCase(fetchDiscoverEvents.pending, (state: EventsState) => {
				state.loadingStates.discoverEvents = 'loading';
				state.error = null;
			})
			.addCase(
				fetchDiscoverEvents.fulfilled,
				(state: EventsState, action: PayloadAction<EventData[]>) => {
					state.loadingStates.discoverEvents = 'succeeded';
					state.discoverEvents = action.payload;
				},
			)
			.addCase(fetchDiscoverEvents.rejected, (state: EventsState, action: any) => {
				state.loadingStates.discoverEvents = 'failed';
				state.error =
					typeof action.payload === 'string'
						? action.payload
						: 'Failed to fetch discover events';
			})

			// Handle fetchPastEvents
			.addCase(fetchPastEvents.pending, (state: EventsState) => {
				state.loadingStates.pastEvents = 'loading';
				state.error = null;
			})
			.addCase(
				fetchPastEvents.fulfilled,
				(state: EventsState, action: PayloadAction<EventData[]>) => {
					state.loadingStates.pastEvents = 'succeeded';
					state.pastEvents = action.payload;
				},
			)
			.addCase(fetchPastEvents.rejected, (state: EventsState, action: any) => {
				state.loadingStates.pastEvents = 'failed';
				state.error =
					typeof action.payload === 'string'
						? action.payload
						: 'Failed to fetch past events';
			})

			// Handle fetchAllEventCategories
			.addCase(fetchAllEventCategories.pending, (state: EventsState) => {
				state.loadingStates.global = 'loading';
				state.error = null;
			})
			.addCase(fetchAllEventCategories.fulfilled, (state: EventsState) => {
				state.loadingStates.global = 'succeeded';
			})
			.addCase(fetchAllEventCategories.rejected, (state: EventsState) => {
				state.loadingStates.global = 'failed';
			})

			// Handle fetchEventDetails
			.addCase(fetchEventDetails.pending, (state: EventsState) => {
				state.loadingStates.selectedEvent = 'loading';
				state.error = null;
			})
			.addCase(
				fetchEventDetails.fulfilled,
				(state: EventsState, action: PayloadAction<EventData>) => {
					state.loadingStates.selectedEvent = 'succeeded';
					state.selectedEvent = action.payload;
				},
			)
			.addCase(fetchEventDetails.rejected, (state: EventsState, action: any) => {
				state.loadingStates.selectedEvent = 'failed';
				state.error =
					typeof action.payload === 'string'
						? action.payload
						: 'Failed to fetch event details';
			})

			// Handle updateEventStatus
			.addCase(updateEventStatus.pending, (state: EventsState) => {
				state.loadingStates.global = 'loading';
				state.error = null;
			})
			.addCase(
				updateEventStatus.fulfilled,
				(
					state: EventsState,
					action: PayloadAction<{ eventId: string; status: string }>,
				) => {
					state.loadingStates.global = 'succeeded';

					// Update the status for the event in all categories
					const categoriesToUpdate: (keyof Pick<
						EventsState,
						'yourEvents' | 'groupEvents' | 'discoverEvents' | 'pastEvents'
					>)[] = ['yourEvents', 'groupEvents', 'discoverEvents', 'pastEvents'];

					// Process each category
					categoriesToUpdate.forEach((category) => {
						const events = state[category];

						// Skip if category doesn't exist or isn't an array
						if (!Array.isArray(events)) return;

						// Update the events array by mapping to a new array
						state[category] = events.map((event) =>
							event.id === action.payload.eventId
								? {
										...event,
										status: action.payload.status as
											| 'going'
											| 'interested'
											| 'not_going',
									}
								: event,
						);
					});

					// Update selected event if it's the one being updated
					if (state.selectedEvent && state.selectedEvent.id === action.payload.eventId) {
						state.selectedEvent = {
							...state.selectedEvent,
							status: action.payload.status as 'going' | 'interested' | 'not_going',
						};
					}
				},
			)
			.addCase(updateEventStatus.rejected, (state: EventsState, action: any) => {
				state.loadingStates.global = 'failed';
				state.error =
					typeof action.payload === 'string'
						? action.payload
						: 'Failed to update event status';
			});
	},
});

// Export actions and reducer
export const { clearEvents, clearError } = eventsSlice.actions;

export default eventsSlice.reducer;
