/**
 * Domain-specific error classes for Events feature
 * Provides better error messages and type safety
 */

/**
 * Base error class for event-related errors
 */
export class EventError extends Error {
	constructor(
		message: string,
		public code?: string,
	) {
		super(message);
		this.name = 'EventError';
		Object.setPrototypeOf(this, EventError.prototype);
	}
}

/**
 * Error thrown when an event is not found
 */
export class EventNotFoundError extends EventError {
	constructor(eventId: string) {
		super(`Event with ID "${eventId}" was not found`, 'EVENT_NOT_FOUND');
		this.name = 'EventNotFoundError';
		Object.setPrototypeOf(this, EventNotFoundError.prototype);
	}
}

/**
 * Error thrown when updating an event fails
 */
export class EventUpdateError extends EventError {
	constructor(eventId: string, details?: string) {
		super(
			`Failed to update event "${eventId}"${details ? `: ${details}` : ''}`,
			'EVENT_UPDATE_FAILED',
		);
		this.name = 'EventUpdateError';
		Object.setPrototypeOf(this, EventUpdateError.prototype);
	}
}

/**
 * Error thrown when fetching events fails
 */
export class EventFetchError extends EventError {
	constructor(category: string, details?: string) {
		super(
			`Failed to fetch ${category} events${details ? `: ${details}` : ''}`,
			'EVENT_FETCH_FAILED',
		);
		this.name = 'EventFetchError';
		Object.setPrototypeOf(this, EventFetchError.prototype);
	}
}

/**
 * Error thrown when event data is invalid
 */
export class EventValidationError extends EventError {
	constructor(
		message: string,
		public fieldName?: string,
	) {
		super(message, 'EVENT_VALIDATION_ERROR');
		this.name = 'EventValidationError';
		Object.setPrototypeOf(this, EventValidationError.prototype);
	}
}

/**
 * Type guard to check if error is an EventError
 */
export function isEventError(error: unknown): error is EventError {
	return error instanceof EventError;
}
