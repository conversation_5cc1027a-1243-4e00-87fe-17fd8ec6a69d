import React from 'react';
import { useTranslation } from 'react-i18next';
import { useHeaderNavStyle } from '@/hooks/useHeaderNavStyle';
import EventsScreen from './screens/EventsScreen';

/**
 * Events Feature Entry Point
 *
 * This component acts as the main entry point for the Events feature
 * and provides the EventsScreen component with necessary context.
 */
export default function Events() {
	const { t } = useTranslation();

	// Apply header styling
	useHeaderNavStyle({ title: t('features.events.title') });

	return <EventsScreen />;
}
