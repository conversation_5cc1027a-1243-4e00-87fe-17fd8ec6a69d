/**
 * Validation schemas for Events feature
 * Simple runtime validation without external dependencies
 */

const EVENT_ID_REGEX = /^[a-zA-Z0-9-_]+$/;
const RSVP_STATUSES = ['going', 'interested', 'not_going'] as const;
const EVENT_TYPES = ['current', 'upcoming', 'previous', 'discover'] as const;

class ValidationError extends Error {
	constructor(message: string) {
		super(message);
		this.name = 'ValidationError';
	}
}

/**
 * Validate event ID
 * @throws ValidationError if validation fails
 */
export function validateEventId(eventId: string): string {
	if (!eventId || eventId.length === 0) {
		throw new ValidationError('Event ID cannot be empty');
	}
	if (eventId.length > 100) {
		throw new ValidationError('Event ID is too long');
	}
	if (!EVENT_ID_REGEX.test(eventId)) {
		throw new ValidationError('Event ID contains invalid characters');
	}
	return eventId;
}

/**
 * Validate RSVP status
 * @throws ValidationError if validation fails
 */
export function validateRSVPStatus(status: string): 'going' | 'interested' | 'not_going' {
	if (!RSVP_STATUSES.includes(status as any)) {
		throw new ValidationError(
			`Invalid RSVP status. Must be one of: ${RSVP_STATUSES.join(', ')}`,
		);
	}
	return status as 'going' | 'interested' | 'not_going';
}

/**
 * Validate event type
 * @throws ValidationError if validation fails
 */
export function validateEventType(type: string): 'current' | 'upcoming' | 'previous' | 'discover' {
	if (!EVENT_TYPES.includes(type as any)) {
		throw new ValidationError(`Invalid event type. Must be one of: ${EVENT_TYPES.join(', ')}`);
	}
	return type as 'current' | 'upcoming' | 'previous' | 'discover';
}

/**
 * Safe validation that returns result with error instead of throwing
 */
export function safeValidateEventId(
	eventId: string,
): { success: true; data: string } | { success: false; error: string } {
	try {
		const data = validateEventId(eventId);
		return { success: true, data };
	} catch (error) {
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Invalid event ID',
		};
	}
}

/**
 * Safe validation for RSVP status
 */
export function safeValidateRSVPStatus(
	status: string,
):
	| { success: true; data: 'going' | 'interested' | 'not_going' }
	| { success: false; error: string } {
	try {
		const data = validateRSVPStatus(status);
		return { success: true, data };
	} catch (error) {
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Invalid RSVP status',
		};
	}
}
