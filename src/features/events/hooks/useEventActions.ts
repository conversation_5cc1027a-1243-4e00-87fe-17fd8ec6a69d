import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store';
import {
	fetchEventDetails,
	updateEventStatus,
	fetchAllEventCategories,
	fetchYourEvents,
	fetchGroupEvents,
	fetchDiscoverEvents,
	fetchPastEvents,
	clearError,
} from '../eventsSlice';
import type { EventData } from '../model/types';
import { logger } from '@/utils/logger';

const hookLogger = logger.child('useEventActions');

/**
 * Loading states for different event categories
 */
export interface EventLoadingStates {
	yourEvents: boolean;
	groupEvents: boolean;
	discoverEvents: boolean;
	pastEvents: boolean;
	selectedEvent: boolean;
	global: boolean;
}

/**
 * Return type interface for useEventActions hook
 * Provides type-safe access to events state and actions
 */
export interface UseEventActionsReturn {
	// State
	yourEvents: EventData[];
	groupEvents: EventData[];
	discoverEvents: EventData[];
	pastEvents: EventData[];
	selectedEvent: EventData | null;
	loadingStates: EventLoadingStates;
	error: string | null;

	// Actions
	fetchEventDetailsById: (eventId: string) => void;
	updateEventRSVPStatus: (eventId: string, status: 'going' | 'interested' | 'not_going') => void;
	clearEventError: () => void;
	fetchAllEventCategories: () => void;
	fetchYourEvents: () => void;
	fetchGroupEvents: () => void;
	fetchDiscoverEvents: () => void;
	fetchPastEvents: () => void;
}

/**
 * Custom hook for event actions
 *
 * Provides a unified interface for event-related actions and state
 *
 */
export const useEventActions = (): UseEventActionsReturn => {
	const dispatch = useDispatch<AppDispatch>();

	// Select events state from Redux
	const {
		yourEvents,
		groupEvents,
		discoverEvents,
		pastEvents,
		selectedEvent,
		loadingStates: rawLoadingStates,
		error,
	} = useSelector((state: RootState) => state.events);

	// Convert loading states to booleans for easier consumption
	const loadingStates: EventLoadingStates = {
		yourEvents: rawLoadingStates.yourEvents === 'loading',
		groupEvents: rawLoadingStates.groupEvents === 'loading',
		discoverEvents: rawLoadingStates.discoverEvents === 'loading',
		pastEvents: rawLoadingStates.pastEvents === 'loading',
		selectedEvent: rawLoadingStates.selectedEvent === 'loading',
		global: rawLoadingStates.global === 'loading',
	};

	/**
	 * Fetch all event categories at once
	 */
	const fetchAllCategories = useCallback(() => {
		hookLogger.debug('Fetching all event categories');
		try {
			dispatch(fetchAllEventCategories());
		} catch (error) {
			hookLogger.error('Error dispatching fetchAllEventCategories:', error);
		}
	}, [dispatch]);

	/**
	 * Fetch event details by ID
	 * @param eventId - ID of the event to fetch details for
	 */
	const fetchEventDetailsById = useCallback(
		(eventId: string) => {
			dispatch(fetchEventDetails(eventId));
		},
		[dispatch],
	);

	/**
	 * Update event RSVP status
	 * @param eventId - ID of the event to update
	 * @param status - New RSVP status
	 */
	const updateEventRSVPStatus = useCallback(
		(eventId: string, status: 'going' | 'interested' | 'not_going') => {
			dispatch(updateEventStatus({ eventId, status }));
		},
		[dispatch],
	);

	/**
	 * Clear error state
	 */
	const clearEventError = useCallback(() => {
		dispatch(clearError());
	}, [dispatch]);

	/**
	 * Fetch individual event categories
	 */
	const fetchYourEventsData = useCallback(() => {
		dispatch(fetchYourEvents());
	}, [dispatch]);

	const fetchGroupEventsData = useCallback(() => {
		dispatch(fetchGroupEvents());
	}, [dispatch]);

	const fetchDiscoverEventsData = useCallback(() => {
		dispatch(fetchDiscoverEvents());
	}, [dispatch]);

	const fetchPastEventsData = useCallback(() => {
		dispatch(fetchPastEvents());
	}, [dispatch]);

	// Return all event actions and state
	return {
		// State
		yourEvents,
		groupEvents,
		discoverEvents,
		pastEvents,
		selectedEvent,
		loadingStates,
		error,

		// Actions
		fetchEventDetailsById,
		updateEventRSVPStatus,
		clearEventError,
		fetchAllEventCategories: fetchAllCategories,
		fetchYourEvents: fetchYourEventsData,
		fetchGroupEvents: fetchGroupEventsData,
		fetchDiscoverEvents: fetchDiscoverEventsData,
		fetchPastEvents: fetchPastEventsData,
	};
};

export default useEventActions;
