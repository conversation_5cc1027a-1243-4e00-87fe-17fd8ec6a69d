import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Platform } from 'react-native';
import { useTranslation } from 'react-i18next';
import { EventData } from '../model/types';
import {
	formatAttendeeCount,
	formatEventDayOfWeek,
	formatEventFullDate,
} from '../model/transformers';
import { JellyIcon } from '@/components/ui/JellyIcon';
import RSVPButton from './RSVPButton';
import EventFallbackIcon from './EventFallbackIcon';

// Constants for consistent sizing
const CARD_WIDTH = 320;
const IMAGE_SIZE = 66;
const IMAGE_BORDER_RADIUS = 4;
const CARD_BORDER_RADIUS = 12;
const CARD_PADDING = 16;
const ICON_SIZE = 14;
const MESSAGE_ICON_SIZE = 18;
const MESSAGE_BUTTON_WIDTH = 48;

// Default placeholder image for events without thumbnails
const defaultEventImage = require('@assets/images/event-placeholder.png');
interface EventCardProps {
	event: EventData;
	onPress: (eventId: string) => void;
	onStatusChange: (eventId: string, status: 'going' | 'interested' | 'not_going') => void;
	onMessagePress?: (eventId: string) => void; // Optional - message feature may not be enabled
	fullWidth?: boolean; // Use full width for single-item sections
}

/**
 * Event Card Component
 *
 * Displays event information in a card format that matches the wireframe design
 */
export const EventCard: React.FC<EventCardProps> = ({
	event,
	onPress,
	onStatusChange,
	onMessagePress,
	fullWidth = false,
}) => {
	const { t } = useTranslation();
	const [imageError, setImageError] = useState(false);

	// Format date and attendee information using utility functions
	const dayOfWeek = formatEventDayOfWeek(event.date);
	const fullDate = formatEventFullDate(event.date);
	const attendeeCount = formatAttendeeCount(event.attendees, t);

	// Handle event press
	const handleCardPress = () => {
		if (onPress) {
			onPress(event.id);
		}
	};

	// Handle message press
	const handleMessagePress = () => {
		if (onMessagePress) {
			onMessagePress(event.id);
		}
	};

	// Render event image with fallback logic
	const renderEventImage = () => {
		// No thumbnail - use default placeholder
		if (!event.thumbnail) {
			return <Image source={defaultEventImage} style={styles.eventImage} />;
		}

		// Thumbnail failed to load - use fallback icon
		if (imageError) {
			return <EventFallbackIcon size={IMAGE_SIZE} iconSize={28} />;
		}

		// Display thumbnail image
		return (
			<Image
				source={{ uri: event.thumbnail }}
				style={styles.eventImage}
				onError={() => setImageError(true)}
			/>
		);
	};

	return (
		<TouchableOpacity
			style={[styles.container, fullWidth && styles.containerFullWidth]}
			onPress={handleCardPress}
			accessible={true}
			accessibilityRole='button'
			accessibilityHint={t('features.events.a11y.viewEventDetails')}
			accessibilityLabel={`${event.title}, on ${dayOfWeek}, ${fullDate}`}
		>
			<View style={styles.cardContent}>
				{/* Left side - Image */}
				<View style={styles.imageContainer}>{renderEventImage()}</View>

				{/* Right side - Event info */}
				<View style={styles.eventInfo}>
					{/* Date */}
					<Text style={styles.dayText}>{dayOfWeek}</Text>
					<Text style={styles.dateText}>{fullDate}</Text>

					{/* Title */}
					<Text style={styles.title} numberOfLines={2}>
						{event.title}
					</Text>

					{/* Location */}
					<View style={styles.infoRow}>
						<JellyIcon
							name='map'
							size={ICON_SIZE}
							color='#333'
							style={styles.infoIcon}
						/>
						<Text style={styles.infoText} numberOfLines={1}>
							{event.virtual ? t('features.events.location.virtual') : event.location}
						</Text>
					</View>

					{/* Attendees */}
					<View style={styles.infoRow}>
						<JellyIcon
							name='user'
							size={ICON_SIZE}
							color='#333'
							style={styles.infoIcon}
						/>
						<Text style={styles.infoText}>{attendeeCount}</Text>
					</View>

					{/* Spacer to push actions to bottom */}
					<View style={styles.spacer} />
				</View>
			</View>

			{/* Action buttons */}
			<View style={styles.actionsContainer}>
				<RSVPButton status={event.status} eventId={event.id} onPress={onStatusChange} />

				<TouchableOpacity
					style={styles.messageButton}
					onPress={handleMessagePress}
					accessible={true}
					accessibilityLabel={t('features.events.a11y.messageOrganizer')}
					accessibilityRole='button'
				>
					<JellyIcon name='comment' size={MESSAGE_ICON_SIZE} color='#333' />
				</TouchableOpacity>
			</View>
		</TouchableOpacity>
	);
};

const styles = StyleSheet.create({
	container: {
		backgroundColor: '#ffffff',
		borderRadius: CARD_BORDER_RADIUS,
		marginVertical: 8,
		marginRight: 12,
		width: CARD_WIDTH,
		minHeight: 220,
		overflow: 'hidden',
		...Platform.select({
			ios: {
				shadowColor: '#000',
				shadowOffset: { width: 0, height: 2 },
				shadowOpacity: 0.1,
				shadowRadius: 4,
			},
			android: {
				elevation: 2,
			},
		}),
	},
	containerFullWidth: {
		width: 'auto',
		marginHorizontal: CARD_PADDING,
		marginRight: CARD_PADDING,
	},
	cardContent: {
		flexDirection: 'row',
		padding: CARD_PADDING,
		flex: 1,
	},
	imageContainer: {
		width: IMAGE_SIZE,
		height: IMAGE_SIZE,
		marginRight: CARD_PADDING,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#f5f5f5',
		borderRadius: IMAGE_BORDER_RADIUS,
		overflow: 'hidden',
	},
	eventImage: {
		width: IMAGE_SIZE,
		height: IMAGE_SIZE,
		borderRadius: IMAGE_BORDER_RADIUS,
		backgroundColor: '#f5f5f5',
		resizeMode: 'contain',
	},
	eventInfo: {
		flex: 1,
	},
	dayText: {
		fontSize: 12,
		color: '#666',
		marginBottom: 2,
	},
	dateText: {
		fontSize: 14,
		color: '#333',
		fontWeight: '600',
		marginBottom: 8,
	},
	title: {
		fontSize: 18,
		fontWeight: 'bold',
		color: '#222',
		marginBottom: 8,
	},
	infoRow: {
		flexDirection: 'row',
		alignItems: 'center',
		marginBottom: 4,
	},
	infoIcon: {
		marginRight: 6,
	},
	infoText: {
		fontSize: 14,
		color: '#555',
	},
	spacer: {
		flex: 1,
	},
	actionsContainer: {
		flexDirection: 'row',
		borderTopWidth: 1,
		borderTopColor: '#eee',
	},
	messageButton: {
		width: MESSAGE_BUTTON_WIDTH,
		alignItems: 'center',
		justifyContent: 'center',
		backgroundColor: '#f9f9f9',
	},
});

export default EventCard;
