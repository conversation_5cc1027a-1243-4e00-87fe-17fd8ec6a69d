import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';

interface EmptyYourEventsProps {
	onDiscoverPress?: () => void;
}

/**
 * Empty state for Your Events section
 *
 * Displays a message and CTA button when there are no events in Your Events
 */
const EmptyYourEvents: React.FC<EmptyYourEventsProps> = ({ onDiscoverPress }) => {
	const { t } = useTranslation();

	const handleDiscoverPress = () => {
		if (onDiscoverPress) {
			onDiscoverPress();
		} else {
			// Default action: navigate to discover events
			router.push('/(tabs)/more/events/discover');
		}
	};

	return (
		<View style={styles.container}>
			<Text style={styles.title}>{t('features.events.empty.yourEvents.title')}</Text>
			<Text style={styles.description}>
				{t('features.events.empty.yourEvents.description')}
			</Text>
			<Text style={styles.description}>
				{t('features.events.empty.yourEvents.description2')}
			</Text>

			<TouchableOpacity
				style={styles.button}
				onPress={handleDiscoverPress}
				accessibilityLabel={t('features.events.empty.yourEvents.a11y.discoverLabel')}
				accessibilityRole='button'
			>
				<Text style={styles.buttonText}>
					{t('features.events.empty.yourEvents.discoverButton')}
				</Text>
			</TouchableOpacity>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		width: '100%',
		backgroundColor: '#f5f5f5',
		borderRadius: 12,
		padding: 24,
		alignItems: 'center',
		marginHorizontal: 16,
		marginBottom: 16,
	},
	title: {
		fontSize: 18,
		fontWeight: '600',
		color: '#333',
		marginBottom: 8,
		textAlign: 'center',
	},
	description: {
		fontSize: 14,
		color: '#666',
		textAlign: 'center',
		marginBottom: 4,
	},
	button: {
		backgroundColor: '#222',
		borderRadius: 8,
		paddingVertical: 12,
		paddingHorizontal: 24,
		marginTop: 16,
	},
	buttonText: {
		color: '#fff',
		fontSize: 16,
		fontWeight: '600',
	},
});

export default EmptyYourEvents;
