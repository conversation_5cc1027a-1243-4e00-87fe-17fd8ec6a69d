import React, { Component, ErrorInfo, ReactNode } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { logger } from '@/utils/logger';

const errorLogger = logger.child('EventsErrorBoundary');

interface Props {
	children: ReactNode;
	fallback?: ReactNode;
	onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
	hasError: boolean;
	error: Error | null;
}

/**
 * Error Boundary for Events Feature
 *
 * Catches JavaScript errors anywhere in the events component tree,
 * logs those errors, and displays a fallback UI instead of crashing.
 *
 * Usage:
 * ```tsx
 * <EventsErrorBoundary>
 *   <EventDetailScreen eventId={id} />
 * </EventsErrorBoundary>
 * ```
 */
export class EventsErrorBoundary extends Component<Props, State> {
	constructor(props: Props) {
		super(props);
		this.state = {
			hasError: false,
			error: null,
		};
	}

	static getDerivedStateFromError(error: Error): State {
		// Update state so the next render will show the fallback UI
		return {
			hasError: true,
			error,
		};
	}

	componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
		// Log the error to the console and logging service
		errorLogger.error('Events component error caught by boundary', {
			error: error.message,
			stack: error.stack,
			componentStack: errorInfo.componentStack,
		});

		// Call custom error handler if provided
		if (this.props.onError) {
			this.props.onError(error, errorInfo);
		}
	}

	handleReset = (): void => {
		this.setState({
			hasError: false,
			error: null,
		});
	};

	render(): ReactNode {
		if (this.state.hasError) {
			// Custom fallback UI if provided
			if (this.props.fallback) {
				return this.props.fallback;
			}

			// Default fallback UI
			return (
				<View style={styles.container}>
					<View style={styles.content}>
						<Text style={styles.title}>Oops! Something went wrong</Text>
						<Text style={styles.message}>
							We encountered an error while loading the events.
						</Text>

						{__DEV__ && this.state.error && (
							<View style={styles.errorDetails}>
								<Text style={styles.errorTitle}>Error Details:</Text>
								<Text style={styles.errorText}>{this.state.error.message}</Text>
							</View>
						)}

						<TouchableOpacity
							style={styles.button}
							onPress={this.handleReset}
							accessibilityLabel='Try again'
							accessibilityRole='button'
						>
							<Text style={styles.buttonText}>Try Again</Text>
						</TouchableOpacity>
					</View>
				</View>
			);
		}

		return this.props.children;
	}
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#f9f9f9',
		padding: 20,
	},
	content: {
		maxWidth: 400,
		alignItems: 'center',
	},
	title: {
		fontSize: 24,
		fontWeight: 'bold',
		color: '#333',
		marginBottom: 12,
		textAlign: 'center',
	},
	message: {
		fontSize: 16,
		color: '#666',
		textAlign: 'center',
		marginBottom: 24,
		lineHeight: 24,
	},
	errorDetails: {
		backgroundColor: '#fff',
		borderRadius: 8,
		padding: 16,
		marginBottom: 24,
		width: '100%',
		borderWidth: 1,
		borderColor: '#e0e0e0',
	},
	errorTitle: {
		fontSize: 14,
		fontWeight: '600',
		color: '#e53935',
		marginBottom: 8,
	},
	errorText: {
		fontSize: 12,
		color: '#666',
		fontFamily: 'monospace',
	},
	button: {
		backgroundColor: '#5c6bc0',
		paddingHorizontal: 32,
		paddingVertical: 14,
		borderRadius: 8,
		elevation: 2,
		shadowColor: '#000',
		shadowOffset: { width: 0, height: 2 },
		shadowOpacity: 0.1,
		shadowRadius: 4,
	},
	buttonText: {
		color: '#fff',
		fontSize: 16,
		fontWeight: '600',
	},
});

export default EventsErrorBoundary;
