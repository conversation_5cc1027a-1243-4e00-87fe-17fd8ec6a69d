import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { JellyIcon } from '@/components/ui/JellyIcon';
import RSVPModal from './RSVPModal';
import { EventStatus } from '../types/shared';

// TODO: confirm/reconcile these with <PERSON>'s data model
// Extended status type including 'attended' which is used for past events
// This status is display-only and cannot be changed by the user
type ExtendedEventStatus = EventStatus | 'attended';

interface RSVPButtonProps {
	eventId: string;
	status?: ExtendedEventStatus;
	onPress?: (eventId: string, status: EventStatus) => void;
	style?: any;
}

/**
 * RSVP Button Component
 */
const RSVPButton: React.FC<RSVPButtonProps> = ({ status, eventId, onPress }) => {
	const { t } = useTranslation();
	const [modalVisible, setModalVisible] = useState(false);

	// Different labels and icons based on status
	const buttonConfig: Record<string, { label: string; icon?: string }> = {
		going: { label: t('features.events.rsvp.going'), icon: 'check' },
		interested: { label: t('features.events.rsvp.interested'), icon: 'bookmark' },
		not_going: { label: t('features.events.rsvp.notGoing'), icon: 'xmark' },
		attended: { label: t('features.events.rsvp.attended'), icon: 'check' },
		undefined: { label: t('features.events.rsvp.buttonLabel'), icon: 'star' },
	};

	const handlePress = () => {
		// Don't show modal for attended events
		if (status === 'attended') return;

		// Show RSVP modal for selection
		setModalVisible(true);
	};

	const handleSelect = (newStatus: EventStatus) => {
		if (onPress) {
			onPress(eventId, newStatus);
		}
	};

	// Don't allow changing status for attended events
	const isDisabled = status === 'attended';
	const config = buttonConfig[status || 'undefined'];

	return (
		<>
			<TouchableOpacity
				style={[
					styles.button,
					status === 'going' && styles.goingButton,
					status === 'interested' && styles.interestedButton,
					status === 'not_going' && styles.notGoingButton,
					status === 'attended' && styles.attendedButton,
				]}
				onPress={handlePress}
				disabled={isDisabled}
				accessible={true}
				accessibilityLabel={`${config.label} button`}
				accessibilityRole='button'
				accessibilityHint={
					isDisabled
						? t('features.events.rsvp.a11y.eventPassed')
						: t('features.events.rsvp.a11y.changeStatus')
				}
			>
				<View style={styles.buttonContent}>
					{config.icon && (
						<JellyIcon
							name={config.icon as any}
							variant='regular'
							color='white'
							size={16}
							style={styles.icon}
						/>
					)}
					<Text style={styles.buttonText}>{config.label}</Text>

					{/* Dropdown chevron icon */}
					{!isDisabled && (
						<JellyIcon
							name='angleDown'
							variant='regular'
							color='white'
							size={12}
							style={styles.chevron}
						/>
					)}
				</View>
			</TouchableOpacity>

			{/* RSVP Selection Modal */}
			<RSVPModal
				visible={modalVisible}
				onClose={() => setModalVisible(false)}
				onSelect={handleSelect}
				currentStatus={status as EventStatus}
			/>
		</>
	);
};

const styles = StyleSheet.create({
	button: {
		flex: 1,
		backgroundColor: '#222',
		paddingVertical: 10,
		alignItems: 'center',
		justifyContent: 'center',
		borderRadius: 4,
	},
	buttonContent: {
		flexDirection: 'row',
		alignItems: 'center',
		justifyContent: 'center',
	},
	goingButton: {
		backgroundColor: '#24a047', // Green for going
	},
	interestedButton: {
		backgroundColor: '#222', // Black for interested
	},
	notGoingButton: {
		backgroundColor: '#767676', // Gray for not going
	},
	attendedButton: {
		backgroundColor: '#666', // Darker gray for attended
	},
	buttonText: {
		color: '#fff',
		fontWeight: '600',
		fontSize: 14,
	},
	icon: {
		marginRight: 6,
	},
	chevron: {
		marginLeft: 6,
	},
});

export default RSVPButton;
