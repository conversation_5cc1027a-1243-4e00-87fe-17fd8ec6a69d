import React from 'react';
import { View, StyleSheet } from 'react-native';
import { JellyIcon } from '@/components/ui/JellyIcon';

interface EventFallbackIconProps {
	size?: number;
	iconSize?: number;
	color?: string;
}

/**
 * A fallback icon to display when event images fail to load
 */
const EventFallbackIcon: React.FC<EventFallbackIconProps> = ({
	size = 66,
	iconSize = 32,
	color = '#888888',
}) => {
	return (
		<View style={[styles.container, { width: size, height: size }]}>
			<JellyIcon name='calendar' size={iconSize} color={color} />
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#f5f5f5',
		borderRadius: 4,
	},
});

export default EventFallbackIcon;
