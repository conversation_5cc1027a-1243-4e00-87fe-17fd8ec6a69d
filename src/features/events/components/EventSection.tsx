import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, FlatList } from 'react-native';
import { useTranslation } from 'react-i18next';
import { EventData } from '../model/types';
import EventCard from './EventCard';
import EmptyYourEvents from './EmptyYourEvents';

interface EventSectionProps {
	title: string;
	events: EventData[];
	onViewAll?: () => void;
	onEventPress?: (eventId: string) => void;
	onStatusChange?: (eventId: string, status: 'going' | 'interested' | 'not_going') => void;
	onMessagePress?: (eventId: string) => void;
	onDiscoverPress?: () => void;
	loading?: boolean;
	error?: string | null;
}

/**
 * EventSection Component
 *
 * Displays a section of events with a title, horizontal list, and "View All" button
 */
const EventSection: React.FC<EventSectionProps> = ({
	title,
	events,
	onViewAll,
	onEventPress,
	onStatusChange,
	onMessagePress,
	onDiscoverPress,
	loading = false,
	error = null,
}) => {
	const { t } = useTranslation();

	if (!loading && !error && events.length === 0) {
		if (title === t('features.events.yourEvents')) {
			return <EmptyYourEvents onDiscoverPress={onDiscoverPress} />;
		}
		// Hide other empty sections
		return null;
	}

	return (
		<View style={styles.sectionContainer}>
			<View style={styles.sectionHeader}>
				<Text style={styles.sectionTitle}>{title}</Text>
				{onViewAll && (
					<TouchableOpacity
						onPress={onViewAll}
						accessibilityLabel={`${t('features.events.viewAll')} ${title}`}
					>
						<Text style={styles.viewAllText}>
							{t('features.events.viewAll')} <Text>›</Text>
						</Text>
					</TouchableOpacity>
				)}
			</View>

			<FlatList
				horizontal={events.length > 1}
				data={events}
				renderItem={({ item }) => (
					<EventCard
						event={item}
						onPress={onEventPress}
						onStatusChange={onStatusChange}
						onMessagePress={onMessagePress}
						fullWidth={events.length === 1}
					/>
				)}
				keyExtractor={(item) => item.id}
				showsHorizontalScrollIndicator={false}
				contentContainerStyle={styles.listContent}
				style={styles.list}
				nestedScrollEnabled={true}
				ListEmptyComponent={
					loading ? (
						<View style={styles.emptyStateContainer}>
							<Text style={styles.emptyStateText}>
								{t('features.events.loading.events')}
							</Text>
						</View>
					) : error ? (
						<View style={styles.emptyStateContainer}>
							<Text style={styles.errorText}>{error}</Text>
						</View>
					) : (
						<View style={styles.emptyStateContainer}>
							<Text style={styles.emptyStateText}>
								{t('features.events.empty.noEvents')}
							</Text>
						</View>
					)
				}
			/>
		</View>
	);
};

const styles = StyleSheet.create({
	sectionContainer: {
		marginBottom: 24,
	},
	sectionHeader: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 16,
		marginBottom: 8,
	},
	sectionTitle: {
		fontSize: 18,
		fontWeight: '600',
		color: '#333',
	},
	viewAllText: {
		color: '#777777', // Changed to dark grey to match wireframe look
		fontSize: 14,
	},
	list: {
		flexGrow: 0,
	},
	listContent: {
		paddingLeft: 16,
		paddingRight: 16,
	},
	emptyStateContainer: {
		width: 280,
		height: 140,
		backgroundColor: '#f5f5f5',
		borderRadius: 12,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
	emptyStateText: {
		color: '#757575',
		fontSize: 16,
	},
	errorText: {
		color: '#e53935',
		fontSize: 16,
		textAlign: 'center',
	},
});

export default EventSection;
