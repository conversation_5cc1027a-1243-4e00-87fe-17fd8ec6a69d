import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';

type RSVPStatus = 'going' | 'interested' | 'not_going';

interface RSVPButtonGroupProps {
	currentStatus: RSVPStatus;
	onStatusChange: (status: RSVPStatus) => void;
}

const RSVP_OPTIONS: { status: RSVPStatus; labelKey: string }[] = [
	{ status: 'going', labelKey: 'features.events.rsvp.going' },
	{ status: 'interested', labelKey: 'features.events.rsvp.interested' },
	{ status: 'not_going', labelKey: 'features.events.rsvp.notGoing' },
];

/**
 * RSVPButtonGroup Component
 *
 * Displays a group of RSVP status buttons with one active at a time
 * Replaces repetitive button rendering in EventDetailScreen
 */
export const RSVPButtonGroup: React.FC<RSVPButtonGroupProps> = ({
	currentStatus,
	onStatusChange,
}) => {
	const { t } = useTranslation();

	return (
		<View style={styles.container}>
			{RSVP_OPTIONS.map(({ status, labelKey }) => (
				<TouchableOpacity
					key={status}
					style={[styles.button, currentStatus === status && styles.activeButton]}
					onPress={() => onStatusChange(status)}
					accessible={true}
					accessibilityLabel={t(labelKey)}
					accessibilityRole='button'
					accessibilityState={{ selected: currentStatus === status }}
				>
					<Text
						style={[
							styles.buttonText,
							currentStatus === status && styles.activeButtonText,
						]}
					>
						{t(labelKey)}
					</Text>
				</TouchableOpacity>
			))}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	button: {
		flex: 1,
		paddingVertical: 12,
		marginHorizontal: 4,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: '#5c6bc0',
		alignItems: 'center',
		justifyContent: 'center',
	},
	activeButton: {
		backgroundColor: '#5c6bc0',
	},
	buttonText: {
		color: '#5c6bc0',
		fontWeight: '600',
	},
	activeButtonText: {
		color: '#fff',
	},
});

export default RSVPButtonGroup;
