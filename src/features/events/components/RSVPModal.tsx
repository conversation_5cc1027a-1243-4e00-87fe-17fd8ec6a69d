import React from 'react';
import {
	View,
	Text,
	StyleSheet,
	TouchableOpacity,
	Modal,
	Pressable,
	SafeAreaView,
} from 'react-native';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/theme';

type RSVPStatus = 'going' | 'interested' | 'not_going';

interface RSVPModalProps {
	visible: boolean;
	onClose: () => void;
	onSelect: (status: RSVPStatus) => void;
	currentStatus?: RSVPStatus;
}

/**
 * RSVP Modal Component
 *
 * Modal that displays RSVP options as radio buttons
 */
const RSVPModal: React.FC<RSVPModalProps> = ({ visible, onClose, onSelect, currentStatus }) => {
	const { t } = useTranslation();
	const theme = useTheme();

	const handleSelect = (status: RSVPStatus) => {
		onSelect(status);
		onClose();
	};

	// Render a radio button for each status option
	const renderOption = (status: RSVPStatus, label: string) => {
		const isSelected = currentStatus === status;

		return (
			<TouchableOpacity
				style={styles.optionContainer}
				onPress={() => handleSelect(status)}
				activeOpacity={0.7}
				accessible={true}
				accessibilityLabel={label}
				accessibilityRole='radio'
				accessibilityState={{ checked: isSelected }}
			>
				<Text style={styles.optionLabel}>{label}</Text>
				<View style={[styles.radioOuter, isSelected && { borderColor: theme.colors.tint }]}>
					{isSelected && (
						<View style={[styles.radioInner, { backgroundColor: theme.colors.tint }]} />
					)}
				</View>
			</TouchableOpacity>
		);
	};

	return (
		<Modal visible={visible} transparent={true} animationType='slide' onRequestClose={onClose}>
			<Pressable style={styles.overlay} onPress={onClose}>
				<View style={styles.modalContainer}>
					<Pressable style={styles.modalContent} onPress={(e) => e.stopPropagation()}>
						<SafeAreaView>
							<View style={styles.handle} />

							<View style={styles.header}>
								<Text style={styles.title}>
									{t('features.events.rsvp.modalTitle')}
								</Text>
							</View>

							<View style={styles.optionsContainer}>
								{renderOption('going', t('features.events.rsvp.going'))}
								{renderOption('interested', t('features.events.rsvp.interested'))}
								{renderOption('not_going', t('features.events.rsvp.notGoing'))}
							</View>
						</SafeAreaView>
					</Pressable>
				</View>
			</Pressable>
		</Modal>
	);
};

const styles = StyleSheet.create({
	overlay: {
		flex: 1,
		backgroundColor: 'transparent', // Removed semi-transparent background
		justifyContent: 'flex-end',
	},
	modalContainer: {
		width: '100%',
	},
	modalContent: {
		backgroundColor: 'white',
		borderTopLeftRadius: 16,
		borderTopRightRadius: 16,
		minHeight: 250,
	},
	handle: {
		width: 40,
		height: 4,
		backgroundColor: '#CCCCCC',
		borderRadius: 2,
		alignSelf: 'center',
		marginTop: 12,
	},
	header: {
		padding: 16,
		borderBottomWidth: 1,
		borderBottomColor: '#EEEEEE',
	},
	title: {
		fontSize: 18,
		fontWeight: '600',
		color: '#333',
		textAlign: 'center',
	},
	optionsContainer: {
		padding: 16,
	},
	optionContainer: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingVertical: 16,
	},
	optionLabel: {
		fontSize: 16,
		color: '#333',
	},
	radioOuter: {
		width: 24,
		height: 24,
		borderRadius: 12,
		borderWidth: 2,
		borderColor: '#CCCCCC',
		justifyContent: 'center',
		alignItems: 'center',
	},
	radioInner: {
		width: 12,
		height: 12,
		borderRadius: 6,
		backgroundColor: '#5c6bc0',
	},
});

export default RSVPModal;
