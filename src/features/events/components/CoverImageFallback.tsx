import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { JellyIcon } from '@/components/ui/JellyIcon';

interface CoverImageFallbackProps {
	width?: number | string;
	height?: number;
	iconSize?: number;
	title?: string;
}

/**
 * A fallback component to display when event cover images fail to load
 */
const CoverImageFallback: React.FC<CoverImageFallbackProps> = ({
	height = 250,
	iconSize = 64,
	title,
}) => {
	const containerStyle = React.useMemo(() => {
		return [styles.container, { height }];
	}, [height]);

	return (
		<View style={containerStyle}>
			<JellyIcon name='calendar' size={iconSize} color='#888888' />
			{title && <Text style={styles.title}>{title}</Text>}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		width: '100%', // Default width to 100%
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#f5f5f5',
		padding: 16,
	},
	title: {
		marginTop: 12,
		fontSize: 16,
		color: '#666666',
		textAlign: 'center',
	},
});

export default CoverImageFallback;
