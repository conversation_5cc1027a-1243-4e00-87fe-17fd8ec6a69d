import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';

interface ErrorViewProps {
	message: string;
	onRetry?: () => void;
	retryText?: string;
}

/**
 * Error View Component
 *
 * Displays an error message with optional retry button
 */
const ErrorView: React.FC<ErrorViewProps> = ({ message, onRetry, retryText }) => {
	const { t } = useTranslation();
	const defaultRetryText = t('features.events.errors.retry');

	return (
		<View style={styles.container}>
			<Text style={styles.errorText}>{message}</Text>
			{onRetry && (
				<TouchableOpacity
					style={styles.retryButton}
					onPress={onRetry}
					accessibilityLabel={retryText || defaultRetryText}
					accessibilityRole='button'
				>
					<Text style={styles.retryButtonText}>{retryText || defaultRetryText}</Text>
				</TouchableOpacity>
			)}
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
	errorText: {
		fontSize: 16,
		color: '#e53935',
		textAlign: 'center',
		marginBottom: 16,
	},
	retryButton: {
		paddingVertical: 8,
		paddingHorizontal: 16,
		backgroundColor: '#5c6bc0',
		borderRadius: 4,
	},
	retryButtonText: {
		color: '#fff',
		fontWeight: '600',
	},
});

export default ErrorView;
