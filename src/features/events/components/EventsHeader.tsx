import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { useRouter } from 'expo-router';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@/theme';
import { JellyIcon } from '@/components/ui/JellyIcon';

interface EventsHeaderProps {
	onSearchPress?: () => void;
}

// TODO: maybe specify using only one of the onSearchPress OR the routing /search path
export default function EventsHeader({ onSearchPress }: EventsHeaderProps) {
	const { t } = useTranslation();
	const theme = useTheme();
	const router = useRouter();

	const handleSearchPress = () => {
		if (onSearchPress) {
			onSearchPress();
		} else {
			// Default search action if no custom handler provided
			router.push('/(tabs)/more/events/search');
		}
	};

	return (
		<View style={styles.headerRight}>
			<TouchableOpacity
				style={styles.iconButton}
				onPress={handleSearchPress}
				accessibilityLabel={t('features.events.a11y.searchEvents')}
			>
				<JellyIcon
					name='magnifyingGlass'
					variant='regular'
					color={theme.colors.text}
					size={24}
				/>
			</TouchableOpacity>
		</View>
	);
}

const styles = StyleSheet.create({
	headerRight: {
		flexDirection: 'row',
		alignItems: 'center',
		paddingRight: 8,
	},
	iconButton: {
		padding: 8,
		borderRadius: 8,
		justifyContent: 'center',
		alignItems: 'center',
	},
});
