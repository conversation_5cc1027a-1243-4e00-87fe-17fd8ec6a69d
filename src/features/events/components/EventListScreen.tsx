import React, { useEffect } from 'react';
import { View, StyleSheet, FlatList, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';
import { useTranslation } from 'react-i18next';
import EventCard from './EventCard';
import ErrorView from './ErrorView';
import Events<PERSON>istHeader from './EventsListHeader';
import useEventActions from '../hooks/useEventActions';
import { EventData } from '../model/types';

export type EventListType = 'your' | 'group' | 'discover' | 'past';

interface EventListScreenProps {
	/**
	 * Type of events to display
	 */
	type: EventListType;
	/**
	 * Translation key for the header label
	 */
	labelKey: string;
	/**
	 * Whether to show the filter button
	 */
	showFilter?: boolean;
	/**
	 * Custom filter press handler
	 */
	onFilterPress?: () => void;
	/**
	 * Custom retry handler for error state
	 */
	onRetry?: () => void;
}

/**
 * Reusable Event List Screen Component
 *
 * A generic screen component that handles different types of event lists
 * (your events, group events, discover events, past events) with a unified interface.
 *
 * This component eliminates code duplication across the four similar event screens
 * while maintaining flexibility for future customizations.
 */
export default function EventListScreen({
	type,
	labelKey,
	showFilter = false,
	onFilterPress,
	onRetry,
}: EventListScreenProps) {
	const { t } = useTranslation();

	// Get the appropriate data and functions based on type
	const eventActions = useEventActions();
	const { loading, error, fetchEventDetailsById, updateEventRSVPStatus } = eventActions;

	// Map event types to their data and fetch functions
	const getEventConfig = () => {
		switch (type) {
			case 'your':
				return {
					events: eventActions.yourEvents,
					fetchFunction: eventActions.fetchYourEvents,
					dependencies: [], // Empty deps for your events
				};
			case 'group':
				return {
					events: eventActions.groupEvents,
					fetchFunction: eventActions.fetchGroupEvents,
					dependencies: [eventActions.fetchGroupEvents],
				};
			case 'discover':
				return {
					events: eventActions.discoverEvents,
					fetchFunction: eventActions.fetchDiscoverEvents,
					dependencies: [eventActions.fetchDiscoverEvents],
				};
			case 'past':
				return {
					events: eventActions.pastEvents,
					fetchFunction: eventActions.fetchPastEvents,
					dependencies: [eventActions.fetchPastEvents],
				};
			default:
				throw new Error(`Unknown event list type: ${type}`);
		}
	};

	const { events, fetchFunction, dependencies } = getEventConfig();

	// Fetch events on mount
	useEffect(() => {
		fetchFunction();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, dependencies);

	// Handle event card press
	const handleEventPress = (eventId: string) => {
		fetchEventDetailsById(eventId);
		router.push(`/(tabs)/more/events/${eventId}`);
	};

	// Handle RSVP press
	const handleStatusChange = (eventId: string, status: 'going' | 'interested' | 'not_going') => {
		updateEventRSVPStatus(eventId, status);
	};

	// Handle retry
	const handleRetry = () => {
		if (onRetry) {
			onRetry();
		} else {
			fetchFunction();
		}
	};

	// Handle filter press
	const handleFilterPress = () => {
		if (onFilterPress) {
			onFilterPress();
		} else {
			console.log(`Filter button pressed for ${type} events`);
		}
	};

	// Loading state
	if (loading) {
		return (
			<View style={styles.loadingContainer}>
				<ActivityIndicator size='large' color='#5c6bc0' />
			</View>
		);
	}

	// Error state
	if (error) {
		return (
			<ErrorView
				message={error}
				retryText={t('features.events.errors.tryAgain')}
				onRetry={handleRetry}
			/>
		);
	}

	// Render event item
	const renderEventItem = ({ item }: { item: EventData }) => (
		<EventCard
			event={item}
			onPress={handleEventPress}
			onStatusChange={handleStatusChange}
			fullWidth={true}
		/>
	);

	// Main content
	return (
		<View style={styles.container}>
			<EventsListHeader
				label={t(labelKey)}
				count={events.length}
				onFilterPress={showFilter ? handleFilterPress : undefined}
			/>

			<FlatList
				data={events}
				renderItem={renderEventItem}
				keyExtractor={(item) => item.id}
				showsVerticalScrollIndicator={false}
				contentContainerStyle={styles.listContainer}
			/>
		</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flex: 1,
		backgroundColor: '#f8f9fa',
	},
	loadingContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		backgroundColor: '#f8f9fa',
	},
	listContainer: {
		padding: 16,
		paddingBottom: 32,
	},
});
