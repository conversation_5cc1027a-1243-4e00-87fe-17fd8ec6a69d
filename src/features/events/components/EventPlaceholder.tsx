import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated, Platform } from 'react-native';

/**
 * Event Placeholder Component
 *
 * Displays a loading placeholder for events with animation
 */
const EventPlaceholder: React.FC = () => {
	const fadeAnim = useRef(new Animated.Value(0.3)).current;

	useEffect(() => {
		// Create a looping pulse animation
		Animated.loop(
			Animated.sequence([
				Animated.timing(fadeAnim, {
					toValue: 0.8,
					duration: 800,
					useNativeDriver: true,
				}),
				Animated.timing(fadeAnim, {
					toValue: 0.3,
					duration: 800,
					useNativeDriver: true,
				}),
			]),
		).start();

		return () => {
			// Clean up animation
			fadeAnim.stopAnimation();
		};
	}, [fadeAnim]);

	return (
		<View style={styles.container}>
			<Animated.View
				style={[
					styles.card,
					{
						opacity: fadeAnim,
					},
				]}
			>
				<View style={styles.contentContainer}>
					<View style={styles.titlePlaceholder} />
					<View style={styles.infoContainer}>
						<View style={styles.datePlaceholder} />
						<View style={styles.locationPlaceholder} />
					</View>
					<View style={styles.attendeePlaceholder} />
					<View style={styles.actionsContainer}>
						<View style={styles.statusButtonPlaceholder} />
						<View style={styles.messageButtonPlaceholder} />
					</View>
				</View>
			</Animated.View>
		</View>
	);
};

const styles = StyleSheet.create({
	container: {
		marginVertical: 8,
		marginHorizontal: 16,
		height: 220,
		borderRadius: 12,
		backgroundColor: '#f5f5f5',
		overflow: 'hidden',
		...Platform.select({
			ios: {
				shadowColor: '#000',
				shadowOffset: { width: 0, height: 2 },
				shadowOpacity: 0.1,
				shadowRadius: 4,
			},
			android: {
				elevation: 2,
			},
		}),
	},
	card: {
		width: '100%',
		height: '100%',
		justifyContent: 'flex-end',
		backgroundColor: '#e0e0e0',
	},
	contentContainer: {
		padding: 16,
	},
	titlePlaceholder: {
		height: 20,
		backgroundColor: '#bdbdbd',
		marginBottom: 12,
		width: '70%',
		borderRadius: 4,
	},
	infoContainer: {
		marginBottom: 8,
	},
	datePlaceholder: {
		height: 14,
		backgroundColor: '#bdbdbd',
		marginBottom: 8,
		width: '40%',
		borderRadius: 4,
	},
	locationPlaceholder: {
		height: 14,
		backgroundColor: '#bdbdbd',
		marginBottom: 12,
		width: '60%',
		borderRadius: 4,
	},
	attendeePlaceholder: {
		height: 12,
		backgroundColor: '#bdbdbd',
		marginBottom: 12,
		width: '30%',
		borderRadius: 4,
	},
	actionsContainer: {
		flexDirection: 'row',
		justifyContent: 'space-between',
	},
	statusButtonPlaceholder: {
		flex: 1,
		height: 36,
		backgroundColor: '#bdbdbd',
		borderRadius: 20,
		marginRight: 8,
	},
	messageButtonPlaceholder: {
		flex: 1,
		height: 36,
		backgroundColor: '#bdbdbd',
		borderRadius: 20,
	},
});

export default EventPlaceholder;
