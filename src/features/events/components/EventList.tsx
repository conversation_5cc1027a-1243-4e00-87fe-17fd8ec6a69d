import React from 'react';
import { View, StyleSheet, FlatList, ActivityIndicator, Text, RefreshControl } from 'react-native';
import { EventData } from '../model/types';
import EventCard from './EventCard';
import EventPlaceholder from './EventPlaceholder';

interface EventListProps {
	events: EventData[];
	loading: boolean;
	error: string | null;
	onRefresh?: () => void;
	onEventPress?: (eventId: string) => void;
	onStatusChange?: (eventId: string, status: 'going' | 'interested' | 'not_going') => void;
	onMessagePress?: (eventId: string) => void;
	refreshing?: boolean;
}

/**
 * Event List Component
 *
 * Displays a list of events with support for loading states,
 * error messages, and pull-to-refresh functionality.
 */
export const EventList: React.FC<EventListProps> = ({
	events,
	loading,
	error,
	onRefresh,
	onEventPress,
	onStatusChange,
	onMessagePress,
	refreshing = false,
}) => {
	const renderItem = ({ item }: { item: EventData }) => (
		<EventCard
			event={item}
			onPress={onEventPress}
			onStatusChange={onStatusChange}
			onMessagePress={onMessagePress}
		/>
	);

	/**
	 * Extract stable unique key for each event item
	 * FlatList requires a keyExtractor to optimize rendering and prevent
	 * unnecessary re-renders. Using event.id ensures each item has a stable,
	 * unique identifier across re-renders and data updates.
	 */
	const keyExtractor = (item: EventData) => item.id;

	// Render placeholder cards while loading
	if (loading && !refreshing && events.length === 0) {
		return (
			<View style={styles.container}>
				{[1, 2, 3].map((i) => (
					<EventPlaceholder key={`placeholder-${i}`} />
				))}
			</View>
		);
	}

	// Render error message
	if (error && !loading && events.length === 0) {
		return (
			<View style={styles.centerContainer}>
				<Text style={styles.errorText}>{error}</Text>
			</View>
		);
	}

	// Render empty state
	if (!loading && events.length === 0) {
		return (
			<View style={styles.centerContainer}>
				<Text style={styles.emptyText}>No events available at this time</Text>
				<Text style={styles.emptySubtext}>Check back later or try another category</Text>
			</View>
		);
	}

	return (
		<FlatList
			data={events}
			renderItem={renderItem}
			keyExtractor={keyExtractor}
			contentContainerStyle={styles.listContent}
			showsVerticalScrollIndicator={false}
			refreshControl={
				onRefresh ? (
					<RefreshControl
						refreshing={refreshing}
						onRefresh={onRefresh}
						colors={['#5c6bc0']}
						tintColor='#5c6bc0'
					/>
				) : undefined
			}
			ListFooterComponent={
				loading && events.length > 0 ? (
					<ActivityIndicator size='small' color='#5c6bc0' style={styles.loadingFooter} />
				) : null
			}
		/>
	);
};

const styles = StyleSheet.create({
	container: {
		flex: 1,
		padding: 8,
	},
	centerContainer: {
		flex: 1,
		justifyContent: 'center',
		alignItems: 'center',
		padding: 16,
	},
	listContent: {
		paddingVertical: 8,
	},
	errorText: {
		fontSize: 16,
		color: '#e53935',
		textAlign: 'center',
	},
	emptyText: {
		fontSize: 16,
		color: '#757575',
		textAlign: 'center',
	},
	emptySubtext: {
		fontSize: 14,
		color: '#9e9e9e',
		textAlign: 'center',
		marginTop: 8,
	},
	loadingFooter: {
		marginVertical: 16,
	},
});

export default EventList;
