import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useTranslation } from 'react-i18next';
import { JellyIcon } from '@/components/ui/JellyIcon';

interface EventsListHeaderProps {
	/** The label text to display (e.g., "Your Events", "Group Events") */
	label: string;
	/** The count of events */
	count: number;
	/** Callback when filter button is pressed */
	onFilterPress?: () => void;
}

/**
 * EventsListHeader Component
 *
 * Displays a header section with event count and filter button.
 * Used across various event list screens.
 */
export default function EventsListHeader({ label, count, onFilterPress }: EventsListHeaderProps) {
	const { t } = useTranslation();

	return (
		<View style={styles.container}>
			<Text style={styles.label}>
				{label}: {count}
			</Text>
			<TouchableOpacity
				style={styles.filterButton}
				onPress={onFilterPress}
				accessibilityLabel={t('features.events.a11y.filterEvents')}
			>
				<JellyIcon name='filter' variant='regular' color='#000000' size={22} />
			</TouchableOpacity>
		</View>
	);
}

const styles = StyleSheet.create({
	container: {
		flexDirection: 'row',
		justifyContent: 'space-between',
		alignItems: 'center',
		paddingHorizontal: 16,
		paddingVertical: 12,
		backgroundColor: '#ffffff',
		borderBottomWidth: 1,
		borderBottomColor: '#e0e0e0',
	},
	label: {
		fontSize: 16,
		fontWeight: '400',
		color: '#000000',
	},
	filterButton: {
		padding: 8,
		borderRadius: 8,
		borderWidth: 1,
		borderColor: '#000000',
		justifyContent: 'center',
		alignItems: 'center',
	},
});
