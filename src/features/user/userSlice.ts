/**
 * User Redux Slice
 *
 * Manages user authentication state and profile data
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { UserState, UserProfile, LoginCredentials } from '@/types/user';
import { createAuthService } from '@/services/authService';
import { createUserService } from '@/services/userService';
import { saveLastLoginEmail, getLastLoginEmail, clearLastLoginEmail, isExpoEnvironment } from '@/utils/devLoginHelper';

// Initial state
const initialState: UserState = {
	isInitialized: false,
	isAuthenticated: false,
	isLoading: false,
	user: null,
	error: null,
};
// Async thunks
export const devLoginUser = createAsyncThunk(
	'user/dev-login',
	async (credentials: LoginCredentials, { rejectWithValue }) => {
		try {
			const authService = await createAuthService();
			const apiUserProfile = await authService.devLogin(credentials.email);
			
			// Save email for auto-login in development
			if (__DEV__) {
				await saveLastLoginEmail(credentials.email);
			}
			
			// Convert from API UserProfile to our UserProfile type
			// This handles type incompatibility between the two UserProfile interfaces
			const userProfile = {
				// Required fields in our UserProfile type
				una_account_id: parseInt(apiUserProfile.id) || 0,
				una_content_id: parseInt(apiUserProfile.id) || 0,
				profile_id: parseInt(apiUserProfile.id) || 0,
				email: apiUserProfile.email || '',
				username: apiUserProfile.username || apiUserProfile.email || '',
				display_name: apiUserProfile.name || apiUserProfile.username || '',
				role: Array.isArray(apiUserProfile.roles) ? apiUserProfile.roles[0] : 'user',
				status: 'active',
				// Optional fields
				avatar_url: apiUserProfile.avatar || undefined,
				// Allow passing through additional fields
				...apiUserProfile,
			};
			
			return userProfile;
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : 'Dev login failed';
			return rejectWithValue(errorMessage);
		}
	},
);

/**
 * Fetch the current user profile or attempt auto-login
 * - First tries to get an existing token
 * - If no token but in dev mode, tries auto-login with saved credentials
 */
export const fetchUser = createAsyncThunk('user/fetch', async (_, { rejectWithValue, dispatch }) => {
	try {
		const authService = await createAuthService();
		// Try to get an existing token
		const token = await authService.getToken();
		
		if (token) {
			// Token exists, fetch the user profile
			const userService = await createUserService();
			const apiUserProfile = await userService.getProfile();
			
			// Convert from API UserProfile to our UserProfile type
			// This handles type incompatibility between the two UserProfile interfaces
			const userProfile = {
				// Required fields in our UserProfile type
				una_account_id: parseInt(apiUserProfile.id) || 0,
				una_content_id: parseInt(apiUserProfile.id) || 0,
				profile_id: parseInt(apiUserProfile.id) || 0,
				email: apiUserProfile.email || '',
				username: apiUserProfile.username || apiUserProfile.email || '',
				display_name: apiUserProfile.name || apiUserProfile.username || '',
				role: Array.isArray(apiUserProfile.roles) ? apiUserProfile.roles[0] : 'user',
				status: 'active',
				// Optional fields
				avatar_url: apiUserProfile.avatar || undefined,
				// Allow passing through additional fields
				...apiUserProfile,
			};
			
			return userProfile;
		}
		
		// No token exists - try auto-login in development
		if (__DEV__ && isExpoEnvironment()) {
			console.log('[Auto-Login] No token found, trying auto-login with saved credentials');
			const lastEmail = await getLastLoginEmail();
			
			if (lastEmail) {
				console.log(`[Auto-Login] Found saved email: ${lastEmail}, attempting auto-login`);
				
				// Dispatch the dev login action
				try {
					// We don't await this because it would create a circular dependency
					// Instead, we'll let the normal auth flow handle the result
					dispatch(devLoginUser({ email: lastEmail, password: '' }));
					console.log('[Auto-Login] Auto-login initiated');
				} catch (loginError) {
					console.warn('[Auto-Login] Auto-login attempt failed:', loginError);
				}
			}
		}
		
		// Return null to indicate no authenticated user
		return null;
	} catch (error) {
		const errorMessage = error instanceof Error ? error.message : 'Fetch user failed';
		console.error('[User] Error fetching user:', errorMessage);
		return rejectWithValue(errorMessage);
	}
});

export const logoutUser = createAsyncThunk('user/logout', async () => {
	const authService = await createAuthService();
	
	// Clear server-side session and local token
	await authService.logout();
	
	// In development, also clear saved login credentials
	if (__DEV__) {
		try {
			await clearLastLoginEmail();
			console.log('[Logout] Cleared saved login credentials');
		} catch (error) {
			console.error('[Logout] Error clearing saved credentials:', error);
		}
	}
});

// User slice
const userSlice = createSlice({
	name: 'user',
	initialState,
	reducers: {
		logout: (state) => {
			state.isAuthenticated = false;
			state.user = null;
			state.error = null;
		},
		clearError: (state) => {
			state.error = null;
		},
		setUser: (state, action: PayloadAction<UserProfile>) => {
			console.log('👤 User data updated in Redux store:', action.payload);
			state.user = action.payload;
			state.isAuthenticated = true;
			state.error = null;
		},
	},
	extraReducers: (builder) => {
		// Dev login user
		builder
			.addCase(devLoginUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
				state.user = null;
			})
			.addCase(devLoginUser.fulfilled, (state, action) => {
				console.log('🔐 Dev login successful - user profile returned');
				state.isLoading = false;
				state.isAuthenticated = true;
				state.user = action.payload as UserProfile;
				state.error = null;
			})
			.addCase(devLoginUser.rejected, (state, action) => {
				state.isLoading = false;
				state.isAuthenticated = false;
				state.user = null;
				state.error = action.payload as string;
			});

		// Verify email
		builder
			.addCase(fetchUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
				state.isInitialized = true;
				state.user = null;
				state.isAuthenticated = false;
				console.log('User profile fetch initiated...');
			})
			.addCase(fetchUser.fulfilled, (state, action) => {
				state.isLoading = false;
				if (action.payload === null) {
					// No token present; not an error state. Just mark not authenticated.
					state.isAuthenticated = false;
					state.error = null;
				} else {
					state.isAuthenticated = true;
					state.user = action.payload;
					state.error = null;
				}
			})
			.addCase(fetchUser.rejected, (state, action) => {
				console.error('❌ Error fetching user profile:', action.payload);
				state.isLoading = false;
				state.error = action.payload as string;
				state.user = null;
				state.isAuthenticated = false;
			});

		// Logout user
		builder
			.addCase(logoutUser.pending, (state) => {
				state.isLoading = true;
				state.error = null;
			})
			.addCase(logoutUser.fulfilled, (state) => {
				state.isLoading = false;
				state.isAuthenticated = false;
				state.user = null;
				state.error = null;
				state.isInitialized = false;
				console.log('🚪 User logged out successfully');
			})
			.addCase(logoutUser.rejected, (state, action) => {
				state.isLoading = false;
				state.error = action.payload as string;
			});
	},
});

export const { logout, clearError, setUser } = userSlice.actions;
export default userSlice.reducer;

// Selectors
export const selectUser = (state: { user: UserState }) => state.user.user;
export const selectIsAuthenticated = (state: { user: UserState }) => state.user.isAuthenticated;
export const selectIsLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectError = (state: { user: UserState }) => state.user.error;
export const selectIsInitialized = (state: { user: UserState }) => state.user.isInitialized;
